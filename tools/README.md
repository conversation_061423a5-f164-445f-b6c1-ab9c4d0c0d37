# 工具目录

本目录包含开发和运维过程中使用的各种工具脚本。

## 📁 目录结构

```
tools/
├── setup_ssh.py           # SSH配置助手
└── README.md               # 本文档
```

## 🔧 工具说明

### SSH配置助手 (setup_ssh.py)

自动生成SSH配置和连接命令的工具。

#### 功能特性

- **交互式配置**: 通过问答方式收集SSH连接信息
- **自动生成配置**: 生成SSH config文件配置
- **端口转发**: 支持本地端口转发配置
- **连接脚本**: 自动生成连接脚本文件
- **安全检查**: 验证SSH密钥和权限设置

#### 使用方法

```bash
# 运行SSH配置助手
python tools/setup_ssh.py

# 按提示输入以下信息：
# - 服务器IP地址
# - SSH用户名
# - 本地端口（默认5000）
# - 远程端口（默认5000）
# - SSH密钥路径（可选）
```

#### 生成的文件

工具会在项目根目录生成以下文件：

1. **SSH配置片段**: 可添加到 `~/.ssh/config`
2. **连接脚本**: 一键连接的shell脚本
3. **端口转发脚本**: 设置端口转发的脚本

#### 示例输出

```bash
# 生成的SSH配置
Host myserver
    HostName *************
    User myuser
    Port 22
    LocalForward 5000 localhost:5000
    IdentityFile ~/.ssh/id_rsa
```

```bash
# 生成的连接脚本
#!/bin/bash
ssh -L 5000:localhost:5000 myuser@*************
```

## 🚀 使用场景

### 开发环境设置

当需要连接到远程开发服务器时：

1. 运行SSH配置助手
2. 输入服务器连接信息
3. 使用生成的脚本连接服务器
4. 通过端口转发访问远程服务

### 部署和运维

在部署过程中需要SSH连接时：

1. 为不同环境生成不同的SSH配置
2. 使用标准化的连接方式
3. 确保端口转发配置正确

## 📝 配置示例

### 开发环境

```bash
服务器IP: **************
用户名: developer
本地端口: 5000
远程端口: 5000
```

### 生产环境

```bash
服务器IP: **************
用户名: admin
本地端口: 8080
远程端口: 80
```

## 🔒 安全注意事项

1. **密钥管理**: 确保SSH私钥权限设置为600
2. **端口安全**: 只转发必要的端口
3. **用户权限**: 使用最小权限原则
4. **连接验证**: 首次连接时验证服务器指纹

## 📋 最佳实践

1. **标准化配置**: 为不同环境使用一致的配置模式
2. **文档记录**: 记录生成的配置和用途
3. **定期更新**: 定期检查和更新SSH配置
4. **备份配置**: 备份重要的SSH配置文件

## 🆘 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查服务器IP和端口是否正确
   - 确认SSH服务是否运行
   - 验证防火墙设置

2. **密钥认证失败**
   - 检查私钥文件路径
   - 确认私钥权限设置
   - 验证公钥是否已添加到服务器

3. **端口转发失败**
   - 检查本地端口是否被占用
   - 确认远程端口是否可访问
   - 验证端口转发语法

### 调试方法

```bash
# 使用详细模式连接
ssh -v -L 5000:localhost:5000 user@server

# 检查SSH配置
ssh -F ~/.ssh/config -T user@server

# 测试端口连通性
telnet localhost 5000
```

## 🔄 扩展开发

### 添加新工具

1. 在tools目录下创建新的Python脚本
2. 添加适当的文档和使用说明
3. 更新本README文件
4. 确保工具具有执行权限

### 工具模板

```python
#!/usr/bin/env python3
"""
新工具脚本模板
"""
import os
import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description='工具描述')
    parser.add_argument('--option', help='选项说明')
    args = parser.parse_args()
    
    # 工具逻辑
    print("工具执行完成")

if __name__ == "__main__":
    main()
```

## 📞 支持

如需帮助或有建议，请：

1. 查看工具的帮助信息
2. 检查错误输出和日志
3. 参考相关文档
4. 联系开发团队
