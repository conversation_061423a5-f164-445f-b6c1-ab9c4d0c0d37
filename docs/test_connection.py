#!/usr/bin/env python3
"""
测试RabbitMQ连接稳定性的脚本
"""

import time
import json
from app.image_process import get_rabbitmq_connection, send_progress_update

def test_connection_stability():
    """测试连接稳定性"""
    print("开始测试RabbitMQ连接稳定性...")
    
    # 测试1: 基本连接测试
    print("\n=== 测试1: 基本连接测试 ===")
    try:
        with get_rabbitmq_connection() as channel:
            print("✓ 连接成功建立")
            print("✓ 队列声明成功")
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False
    
    # 测试2: 多次连接测试
    print("\n=== 测试2: 多次连接测试 ===")
    for i in range(5):
        try:
            with get_rabbitmq_connection() as channel:
                print(f"✓ 第{i+1}次连接成功")
                time.sleep(1)
        except Exception as e:
            print(f"✗ 第{i+1}次连接失败: {e}")
            return False
    
    # 测试3: 消息发送测试
    print("\n=== 测试3: 消息发送测试 ===")
    try:
        send_progress_update(
            taskId="test_task_001",
            imageId="test_image_001", 
            status=1,
            progress=0.5,
            result="测试消息发送"
        )
        print("✓ 消息发送成功")
    except Exception as e:
        print(f"✗ 消息发送失败: {e}")
        return False
    
    # 测试4: 长时间连接测试
    print("\n=== 测试4: 长时间连接测试 ===")
    try:
        with get_rabbitmq_connection() as channel:
            print("✓ 开始长时间连接测试...")
            for i in range(10):
                # 模拟长时间任务
                time.sleep(2)
                print(f"  - 第{i+1}次心跳检查通过")
            print("✓ 长时间连接测试成功")
    except Exception as e:
        print(f"✗ 长时间连接测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！连接配置正常。")
    return True

def test_celery_config():
    """测试Celery配置"""
    print("\n=== Celery配置检查 ===")
    try:
        from app import celery
        
        # 检查关键配置
        config_checks = [
            ('broker_heartbeat', 300),
            ('task_soft_time_limit', 1800),
            ('task_time_limit', 2400),
            ('broker_connection_timeout', 60),
            ('task_acks_late', True),
        ]
        
        for key, expected in config_checks:
            actual = celery.conf.get(key)
            if actual == expected:
                print(f"✓ {key}: {actual}")
            else:
                print(f"⚠ {key}: {actual} (期望: {expected})")
        
        print("✓ Celery配置检查完成")
        
    except Exception as e:
        print(f"✗ Celery配置检查失败: {e}")

if __name__ == "__main__":
    print("RabbitMQ连接稳定性测试工具")
    print("=" * 50)
    
    # 测试连接
    if test_connection_stability():
        # 测试Celery配置
        test_celery_config()
    else:
        print("\n❌ 连接测试失败，请检查配置")
