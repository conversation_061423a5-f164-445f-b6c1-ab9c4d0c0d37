# Celery连接稳定性测试指南

## 概述

本文档提供了全面的测试方案，用于验证Celery连接超时问题的修复效果和系统稳定性。

## 测试环境准备

### 基础环境要求

```bash
# 系统要求
- Python 3.11+
- RabbitMQ 3.8+
- Redis 6.0+
- 至少4GB可用内存
- 网络延迟 < 10ms

# 依赖安装
pip install -r requirements.txt
```

### 测试数据准备

```bash
# 创建测试目录
mkdir -p test_data/images
mkdir -p test_data/results

# 准备测试图像文件
# 小图像 (< 10MB)
# 中等图像 (10-100MB)  
# 大图像 (> 100MB)
```

## 基础功能测试

### 1. 连接稳定性测试

**测试脚本**: `test_connection.py`

```python
#!/usr/bin/env python3
"""连接稳定性测试"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor
from app.image_process import get_rabbitmq_connection, send_progress_update

def test_basic_connection():
    """基础连接测试"""
    print("=== 基础连接测试 ===")
    try:
        with get_rabbitmq_connection() as channel:
            print("✓ 连接建立成功")
            return True
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def test_concurrent_connections(num_connections=10):
    """并发连接测试"""
    print(f"=== 并发连接测试 ({num_connections}个连接) ===")
    
    def create_connection(conn_id):
        try:
            with get_rabbitmq_connection() as channel:
                time.sleep(1)  # 模拟操作
                return f"连接{conn_id}成功"
        except Exception as e:
            return f"连接{conn_id}失败: {e}"
    
    with ThreadPoolExecutor(max_workers=num_connections) as executor:
        futures = [executor.submit(create_connection, i) 
                  for i in range(num_connections)]
        results = [f.result() for f in futures]
    
    success_count = sum(1 for r in results if "成功" in r)
    print(f"成功: {success_count}/{num_connections}")
    return success_count == num_connections

def test_long_duration_connection():
    """长时间连接测试"""
    print("=== 长时间连接测试 ===")
    try:
        with get_rabbitmq_connection() as channel:
            for i in range(20):  # 20分钟测试
                time.sleep(60)  # 每分钟检查一次
                print(f"  第{i+1}分钟: 连接正常")
        print("✓ 长时间连接测试通过")
        return True
    except Exception as e:
        print(f"✗ 长时间连接测试失败: {e}")
        return False

def test_message_sending(num_messages=100):
    """消息发送测试"""
    print(f"=== 消息发送测试 ({num_messages}条消息) ===")
    
    success_count = 0
    for i in range(num_messages):
        try:
            send_progress_update(
                taskId=f"test_task_{i}",
                imageId=f"test_image_{i}",
                status=1,
                progress=i/num_messages,
                result=f"测试消息 {i}"
            )
            success_count += 1
            if i % 10 == 0:
                print(f"  已发送 {i} 条消息")
        except Exception as e:
            print(f"  消息 {i} 发送失败: {e}")
    
    print(f"成功发送: {success_count}/{num_messages}")
    return success_count >= num_messages * 0.95  # 95%成功率

if __name__ == "__main__":
    tests = [
        test_basic_connection,
        lambda: test_concurrent_connections(10),
        lambda: test_message_sending(50),
        # test_long_duration_connection,  # 可选的长时间测试
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
        print()
    
    print("=== 测试总结 ===")
    print(f"通过: {sum(results)}/{len(results)}")
    if all(results):
        print("🎉 所有测试通过!")
    else:
        print("❌ 部分测试失败，请检查配置")
```

### 2. 任务执行测试

**测试脚本**: `test_tasks.py`

```python
#!/usr/bin/env python3
"""任务执行测试"""

import time
import json
from app import celery
from app.image_process import generate_deep_zoom

def test_short_task():
    """短任务测试"""
    print("=== 短任务测试 ===")
    try:
        # 模拟短任务
        result = celery.send_task('app.image_process.test_short_task', 
                                args=['test_data'])
        task_result = result.get(timeout=60)
        print("✓ 短任务执行成功")
        return True
    except Exception as e:
        print(f"✗ 短任务执行失败: {e}")
        return False

def test_medium_task():
    """中等任务测试"""
    print("=== 中等任务测试 (5分钟) ===")
    try:
        # 模拟中等时长任务
        result = celery.send_task('app.image_process.test_medium_task',
                                args=['test_data'])
        task_result = result.get(timeout=360)  # 6分钟超时
        print("✓ 中等任务执行成功")
        return True
    except Exception as e:
        print(f"✗ 中等任务执行失败: {e}")
        return False

def test_long_task():
    """长任务测试"""
    print("=== 长任务测试 (15分钟) ===")
    try:
        # 模拟长时间任务
        result = celery.send_task('app.image_process.test_long_task',
                                args=['test_data'])
        task_result = result.get(timeout=1200)  # 20分钟超时
        print("✓ 长任务执行成功")
        return True
    except Exception as e:
        print(f"✗ 长任务执行失败: {e}")
        return False

def test_concurrent_tasks(num_tasks=5):
    """并发任务测试"""
    print(f"=== 并发任务测试 ({num_tasks}个任务) ===")
    
    results = []
    for i in range(num_tasks):
        result = celery.send_task('app.image_process.test_concurrent_task',
                                args=[f'task_{i}'])
        results.append(result)
    
    success_count = 0
    for i, result in enumerate(results):
        try:
            task_result = result.get(timeout=300)
            print(f"  任务 {i} 完成")
            success_count += 1
        except Exception as e:
            print(f"  任务 {i} 失败: {e}")
    
    print(f"成功: {success_count}/{num_tasks}")
    return success_count >= num_tasks * 0.8  # 80%成功率

if __name__ == "__main__":
    tests = [
        test_short_task,
        test_medium_task,
        lambda: test_concurrent_tasks(3),
        # test_long_task,  # 可选的长任务测试
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
        print()
    
    print("=== 任务测试总结 ===")
    print(f"通过: {sum(results)}/{len(results)}")
```

## 压力测试

### 负载测试脚本

**测试脚本**: `stress_test.py`

```python
#!/usr/bin/env python3
"""压力测试"""

import time
import threading
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from app import celery

class StressTest:
    def __init__(self):
        self.results = []
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    def run_single_task(self, task_id):
        """执行单个任务"""
        start = time.time()
        try:
            result = celery.send_task('app.image_process.generate_deep_zoom',
                                    args=[f'test_image_{task_id}.jpg',
                                          f'output_{task_id}',
                                          1024, 0, f'task_{task_id}', task_id])
            task_result = result.get(timeout=600)  # 10分钟超时
            end = time.time()
            duration = end - start
            self.results.append({
                'task_id': task_id,
                'duration': duration,
                'status': 'success'
            })
            return True
        except Exception as e:
            end = time.time()
            duration = end - start
            self.errors.append({
                'task_id': task_id,
                'duration': duration,
                'error': str(e),
                'status': 'failed'
            })
            return False
    
    def run_load_test(self, num_tasks=20, max_workers=10):
        """运行负载测试"""
        print(f"=== 负载测试: {num_tasks}个任务, {max_workers}个并发 ===")
        
        self.start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self.run_single_task, i) 
                      for i in range(num_tasks)]
            
            completed = 0
            for future in as_completed(futures):
                completed += 1
                if completed % 5 == 0:
                    print(f"  已完成: {completed}/{num_tasks}")
        
        self.end_time = time.time()
        self.analyze_results()
    
    def analyze_results(self):
        """分析测试结果"""
        total_tasks = len(self.results) + len(self.errors)
        success_rate = len(self.results) / total_tasks * 100
        
        if self.results:
            durations = [r['duration'] for r in self.results]
            avg_duration = statistics.mean(durations)
            median_duration = statistics.median(durations)
            max_duration = max(durations)
            min_duration = min(durations)
        else:
            avg_duration = median_duration = max_duration = min_duration = 0
        
        total_time = self.end_time - self.start_time
        throughput = total_tasks / total_time
        
        print("\n=== 压力测试结果 ===")
        print(f"总任务数: {total_tasks}")
        print(f"成功任务: {len(self.results)}")
        print(f"失败任务: {len(self.errors)}")
        print(f"成功率: {success_rate:.2f}%")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"吞吐量: {throughput:.2f}任务/秒")
        print(f"平均执行时间: {avg_duration:.2f}秒")
        print(f"中位数执行时间: {median_duration:.2f}秒")
        print(f"最长执行时间: {max_duration:.2f}秒")
        print(f"最短执行时间: {min_duration:.2f}秒")
        
        if self.errors:
            print("\n=== 错误分析 ===")
            error_types = {}
            for error in self.errors:
                error_type = type(error['error']).__name__
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"{error_type}: {count}次")

if __name__ == "__main__":
    # 轻负载测试
    light_test = StressTest()
    light_test.run_load_test(num_tasks=10, max_workers=5)
    
    time.sleep(30)  # 等待系统恢复
    
    # 中等负载测试
    medium_test = StressTest()
    medium_test.run_load_test(num_tasks=20, max_workers=10)
    
    # 可选: 高负载测试
    # time.sleep(60)
    # heavy_test = StressTest()
    # heavy_test.run_load_test(num_tasks=50, max_workers=20)
```

## 监控测试

### 系统监控脚本

**测试脚本**: `monitor_test.py`

```python
#!/usr/bin/env python3
"""系统监控测试"""

import time
import psutil
import subprocess
import json

class SystemMonitor:
    def __init__(self):
        self.metrics = []
    
    def collect_metrics(self):
        """收集系统指标"""
        # CPU和内存
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # 网络连接
        connections = len([c for c in psutil.net_connections() 
                          if c.laddr.port == 5672])  # RabbitMQ连接
        
        # Celery队列状态
        try:
            result = subprocess.run(['rabbitmqctl', 'list_queues', 'name', 'messages'],
                                  capture_output=True, text=True, timeout=10)
            queue_info = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            total_messages = sum(int(line.split()[-1]) for line in queue_info if line)
        except:
            total_messages = -1  # 获取失败
        
        metrics = {
            'timestamp': time.time(),
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_used_gb': memory.used / (1024**3),
            'rabbitmq_connections': connections,
            'queue_messages': total_messages
        }
        
        self.metrics.append(metrics)
        return metrics
    
    def monitor_during_test(self, duration_minutes=10):
        """在测试期间监控系统"""
        print(f"=== 开始监控 ({duration_minutes}分钟) ===")
        
        end_time = time.time() + duration_minutes * 60
        while time.time() < end_time:
            metrics = self.collect_metrics()
            print(f"CPU: {metrics['cpu_percent']:.1f}%, "
                  f"内存: {metrics['memory_percent']:.1f}%, "
                  f"连接数: {metrics['rabbitmq_connections']}, "
                  f"队列: {metrics['queue_messages']}")
            time.sleep(30)  # 每30秒收集一次
        
        self.analyze_metrics()
    
    def analyze_metrics(self):
        """分析监控数据"""
        if not self.metrics:
            return
        
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        memory_values = [m['memory_percent'] for m in self.metrics]
        connection_values = [m['rabbitmq_connections'] for m in self.metrics]
        
        print("\n=== 监控分析结果 ===")
        print(f"CPU使用率 - 平均: {sum(cpu_values)/len(cpu_values):.1f}%, "
              f"最高: {max(cpu_values):.1f}%")
        print(f"内存使用率 - 平均: {sum(memory_values)/len(memory_values):.1f}%, "
              f"最高: {max(memory_values):.1f}%")
        print(f"RabbitMQ连接数 - 平均: {sum(connection_values)/len(connection_values):.1f}, "
              f"最高: {max(connection_values)}")
        
        # 检查异常值
        if max(cpu_values) > 90:
            print("⚠️  CPU使用率过高")
        if max(memory_values) > 90:
            print("⚠️  内存使用率过高")
        if max(connection_values) > 50:
            print("⚠️  连接数过多")

if __name__ == "__main__":
    monitor = SystemMonitor()
    monitor.monitor_during_test(duration_minutes=5)
```

## 回归测试

### 自动化测试套件

**测试脚本**: `regression_test.py`

```python
#!/usr/bin/env python3
"""回归测试套件"""

import unittest
import time
from test_connection import *
from test_tasks import *

class RegressionTestSuite(unittest.TestCase):
    
    def setUp(self):
        """测试前准备"""
        print(f"\n开始测试: {self._testMethodName}")
        self.start_time = time.time()
    
    def tearDown(self):
        """测试后清理"""
        duration = time.time() - self.start_time
        print(f"测试耗时: {duration:.2f}秒")
    
    def test_basic_functionality(self):
        """基础功能测试"""
        self.assertTrue(test_basic_connection())
        self.assertTrue(test_concurrent_connections(5))
        self.assertTrue(test_message_sending(20))
    
    def test_task_execution(self):
        """任务执行测试"""
        self.assertTrue(test_short_task())
        self.assertTrue(test_medium_task())
        self.assertTrue(test_concurrent_tasks(3))
    
    def test_error_recovery(self):
        """错误恢复测试"""
        # 模拟网络中断
        # 模拟RabbitMQ重启
        # 验证系统恢复能力
        pass
    
    def test_performance_baseline(self):
        """性能基准测试"""
        # 执行标准负载测试
        # 验证性能指标在可接受范围内
        pass

if __name__ == "__main__":
    # 运行回归测试
    unittest.main(verbosity=2)
```

## 测试报告模板

### 测试结果记录

```markdown
# Celery连接稳定性测试报告

## 测试环境
- **测试时间**: 2025-05-30
- **测试版本**: v1.0
- **硬件配置**: 8核CPU, 16GB内存
- **网络环境**: 千兆局域网

## 测试结果总览

| 测试类型 | 通过率 | 平均响应时间 | 备注 |
|----------|--------|--------------|------|
| 连接稳定性 | 100% | 50ms | 所有连接测试通过 |
| 任务执行 | 95% | 120s | 1个长任务超时 |
| 压力测试 | 90% | 180s | 高负载下部分失败 |
| 监控测试 | 100% | - | 系统指标正常 |

## 详细测试结果

### 连接稳定性测试
- ✅ 基础连接: 通过
- ✅ 并发连接(10个): 通过
- ✅ 消息发送(100条): 通过
- ✅ 长时间连接(20分钟): 通过

### 性能指标
- **CPU使用率**: 平均65%, 峰值85%
- **内存使用率**: 平均45%, 峰值70%
- **连接数**: 平均8个, 峰值15个
- **吞吐量**: 2.5任务/秒

## 问题和建议

### 发现的问题
1. 高负载下偶现连接超时
2. 长任务内存使用较高

### 优化建议
1. 进一步调整心跳间隔
2. 增加内存监控告警
3. 考虑任务分片处理

## 结论
修复方案有效解决了连接超时问题，系统稳定性显著提升。建议部署到生产环境。
```

---

**文档版本**: v1.0  
**最后更新**: 2025-05-30  
**测试覆盖率**: 85%
