#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_info "开始停止标注平台Celery服务..."

# 平台识别配置
PLATFORM_NAME="aiLabel"
CONDA_ENV="myenv"
SIMPLE_PATTERN="celery.*$CONDA_ENV"

log_info "平台标识: $PLATFORM_NAME"
log_info "进程识别模式: $SIMPLE_PATTERN"

# 检查PID文件
if [ -f ./log/celery.pid ]; then
    celery_pid=$(cat ./log/celery.pid)
    log_info "从PID文件读取到进程ID: $celery_pid"

    # 检查进程是否存在
    if kill -0 "$celery_pid" 2>/dev/null; then
        log_info "正在停止Celery进程 (PID: $celery_pid)..."

        # 优雅地停止进程
        kill -TERM "$celery_pid" 2>/dev/null

        # 等待进程停止
        for i in {1..10}; do
            if ! kill -0 "$celery_pid" 2>/dev/null; then
                log_success "Celery进程已成功停止"
                break
            fi
            log_info "等待进程停止... ($i/10)"
            sleep 1
        done

        # 如果进程仍然存在，强制终止
        if kill -0 "$celery_pid" 2>/dev/null; then
            log_warning "进程仍在运行，强制终止..."
            kill -KILL "$celery_pid" 2>/dev/null
            sleep 1

            if ! kill -0 "$celery_pid" 2>/dev/null; then
                log_success "Celery进程已被强制终止"
            else
                log_error "无法终止Celery进程"
            fi
        fi
    else
        log_warning "PID文件中的进程不存在，可能已经停止"
    fi

    # 清理PID文件
    rm -f ./log/celery.pid
    log_info "已清理PID文件"
else
    log_warning "未找到PID文件"
fi

# 查找并清理当前用户的其他标注平台celery进程
log_info "检查是否还有其他标注平台Celery进程..."
current_user=$(whoami)
existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)

if [ -n "$existing_processes" ]; then
    log_warning "发现其他标注平台Celery进程："
    ps aux | grep "$SIMPLE_PATTERN" | grep -v grep || true

    # 进一步筛选，确保是在当前目录运行的进程
    current_dir_processes=""
    for pid in $existing_processes; do
        # 检查进程的工作目录
        proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
        if [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
            current_dir_processes="$current_dir_processes $pid"
        fi
    done

    if [ -n "$current_dir_processes" ]; then
        log_warning "确认发现标注平台Celery进程 (PIDs: $current_dir_processes)"

        # 查找当前用户的标注平台celery进程
        user_processes=""
        for pid in $current_dir_processes; do
            proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
            if [ "$proc_user" = "$current_user" ]; then
                user_processes="$user_processes $pid"
            fi
        done

        if [ -n "$user_processes" ]; then
            log_info "正在清理当前用户($current_user)的标注平台Celery进程 (PIDs: $user_processes)..."

            # 优雅地停止当前用户的进程
            for pid in $user_processes; do
                kill -TERM $pid 2>/dev/null || true
            done
            sleep 3

            # 检查进程是否还存在
            remaining_processes=""
            for pid in $user_processes; do
                if kill -0 $pid 2>/dev/null; then
                    remaining_processes="$remaining_processes $pid"
                fi
            done

            if [ -n "$remaining_processes" ]; then
                log_warning "强制终止剩余的标注平台进程 (PIDs: $remaining_processes)..."
                for pid in $remaining_processes; do
                    kill -KILL $pid 2>/dev/null || true
                done
                sleep 1
            fi

            # 最终检查
            final_remaining=""
            for pid in $user_processes; do
                if kill -0 $pid 2>/dev/null; then
                    final_remaining="$final_remaining $pid"
                fi
            done

            if [ -z "$final_remaining" ]; then
                log_success "当前用户的所有标注平台Celery进程已清理完成"
            else
                log_warning "仍有标注平台Celery进程无法清除 (PIDs: $final_remaining)"
            fi
        else
            log_info "没有发现当前用户($current_user)的标注平台Celery进程"
        fi
    else
        log_info "没有发现在标注平台目录运行的Celery进程"
    fi

    # 最终检查所有celery进程（不干扰其他平台）
    final_check=$(pgrep -f 'celery worker' 2>/dev/null || true)
    if [ -n "$final_check" ]; then
        log_info "系统中仍有其他Celery进程在运行（可能是诊断平台等）："
        ps aux | grep 'celery worker' | grep -v grep | grep -v "$SIMPLE_PATTERN" || true
        log_info "这些进程不会受到影响"
    fi
else
    log_info "没有发现其他标注平台Celery进程"
fi

log_success "标注平台Celery停止脚本执行完成!"
