#!/bin/bash

# 标注平台生产环境Celery停止脚本
# Production Environment Celery Stop Script for aiLabel Platform

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [PROD] INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [PROD] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [PROD] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [PROD] ERROR: $1${NC}"
}

log_info "开始停止标注平台生产环境Celery服务..."

# 环境配置
ENV_TYPE="prod"
LOG_DIR="log/prod"
CONDA_ENV="myenv"

log_info "环境类型: 生产环境 ($ENV_TYPE)"
log_info "日志目录: $LOG_DIR"

# 1. 从PID文件停止进程
if [ -f "$LOG_DIR/celery.pid" ]; then
    celery_pid=$(cat "$LOG_DIR/celery.pid")
    log_info "从PID文件读取到进程ID: $celery_pid"
    
    if kill -0 "$celery_pid" 2>/dev/null; then
        log_info "正在停止生产环境Celery进程 (PID: $celery_pid)..."
        kill -TERM "$celery_pid" 2>/dev/null || true
        
        # 等待进程优雅退出
        for i in {1..15}; do
            if ! kill -0 "$celery_pid" 2>/dev/null; then
                log_success "生产环境Celery进程已优雅退出"
                break
            fi
            log_info "等待进程退出... ($i/15)"
            sleep 1
        done
        
        # 如果进程仍然存在，强制终止
        if kill -0 "$celery_pid" 2>/dev/null; then
            log_warning "进程未能优雅退出，强制终止..."
            kill -KILL "$celery_pid" 2>/dev/null || true
            sleep 2
            
            if kill -0 "$celery_pid" 2>/dev/null; then
                log_error "无法终止进程 $celery_pid"
            else
                log_success "已强制终止生产环境Celery进程"
            fi
        fi
    else
        log_warning "PID文件中的进程不存在，可能已经停止"
    fi
    
    # 清理PID文件
    rm -f "$LOG_DIR/celery.pid"
    log_info "已清理PID文件"
else
    log_warning "未找到PID文件: $LOG_DIR/celery.pid"
fi

# 2. 查找并清理所有生产环境相关的celery进程
log_info "查找并清理所有生产环境Celery进程..."

current_user=$(whoami)
SIMPLE_PATTERN="celery.*$CONDA_ENV.*prod"

existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
if [ -n "$existing_processes" ]; then
    log_warning "发现残留的生产环境Celery进程:"
    ps aux | grep "$SIMPLE_PATTERN" | grep -v grep || true
    
    for pid in $existing_processes; do
        proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
        proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
        if [ "$proc_user" = "$current_user" ] && [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
            log_info "清理生产环境进程 PID: $pid"
            kill -TERM $pid 2>/dev/null || true
        fi
    done
    
    sleep 5
    
    # 强制清理残留进程
    remaining=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
    if [ -n "$remaining" ]; then
        log_warning "强制清理残留的生产环境进程..."
        for pid in $remaining; do
            proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
            if [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
                kill -KILL $pid 2>/dev/null || true
            fi
        done
        sleep 1
    fi
    
    # 最终检查
    final_check=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
    if [ -z "$final_check" ]; then
        log_success "所有生产环境Celery进程已清理完成"
    else
        log_warning "仍有部分生产环境进程无法清理"
        ps aux | grep "$SIMPLE_PATTERN" | grep -v grep || true
    fi
else
    log_info "没有发现运行中的生产环境Celery进程"
fi

# 3. 显示系统中其他Celery进程（不影响它们）
other_celery=$(pgrep -f 'celery worker' 2>/dev/null || true)
if [ -n "$other_celery" ]; then
    log_info "系统中仍有其他Celery进程在运行（开发环境或其他平台）:"
    ps aux | grep 'celery worker' | grep -v grep | grep -v "$SIMPLE_PATTERN" || true
    log_info "这些进程不受影响，继续运行"
fi

log_success "标注平台生产环境Celery停止脚本执行完成!"
