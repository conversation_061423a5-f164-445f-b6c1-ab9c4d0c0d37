#!/bin/bash

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_info "开始启动标注平台Celery服务..."

# 获取当前工作目录，用于进程识别
CURRENT_DIR=$(pwd)
PLATFORM_NAME="aiLabel"
CONDA_ENV="myenv"

log_info "平台标识: $PLATFORM_NAME"
log_info "工作目录: $CURRENT_DIR"
log_info "Conda环境: $CONDA_ENV"

# 1. 激活conda环境
log_info "正在激活conda环境..."
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    source ~/miniconda3/etc/profile.d/conda.sh
    if conda activate $CONDA_ENV; then
        log_success "成功激活conda环境: $CONDA_ENV"
    else
        log_error "激活conda环境失败"
        exit 1
    fi
else
    log_error "找不到conda配置文件"
    exit 1
fi

# 2. 创建日志目录
log_info "创建日志目录..."
mkdir -p log
if [ $? -eq 0 ]; then
    log_success "日志目录创建成功"
else
    log_error "日志目录创建失败"
    exit 1
fi

# 3. 清除已存在的celery进程
log_info "正在清除已存在的标注平台Celery进程..."

# 获取当前用户名
current_user=$(whoami)

# 构建更精确的进程识别模式
# 同时匹配conda环境和工作目录，确保只操作标注平台的进程
PROCESS_PATTERN="celery.*$CONDA_ENV.*aiLabel_python_backend"
SIMPLE_PATTERN="celery.*$CONDA_ENV"

log_info "进程识别模式: $PROCESS_PATTERN"

# 查找并显示现有的celery进程（只查找标注平台相关的）
existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
if [ -n "$existing_processes" ]; then
    log_warning "发现以下可能的标注平台Celery进程:"
    ps aux | grep "$SIMPLE_PATTERN" | grep -v grep || true

    # 进一步筛选，确保是在当前目录运行的进程
    current_dir_processes=""
    for pid in $existing_processes; do
        # 检查进程的工作目录
        proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
        if [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
            current_dir_processes="$current_dir_processes $pid"
        fi
    done

    if [ -n "$current_dir_processes" ]; then
        log_warning "确认发现标注平台Celery进程 (PIDs: $current_dir_processes)"

        # 查找当前用户的标注平台celery进程
        user_processes=""
        for pid in $current_dir_processes; do
            proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
            if [ "$proc_user" = "$current_user" ]; then
                user_processes="$user_processes $pid"
            fi
        done

        if [ -n "$user_processes" ]; then
            log_info "尝试优雅地停止当前用户($current_user)的标注平台Celery进程 (PIDs: $user_processes)..."
            for pid in $user_processes; do
                kill -TERM $pid 2>/dev/null || true
            done

            # 等待进程停止
            sleep 5

            # 检查进程是否还存在
            remaining_processes=""
            for pid in $user_processes; do
                if kill -0 $pid 2>/dev/null; then
                    remaining_processes="$remaining_processes $pid"
                fi
            done

            if [ -n "$remaining_processes" ]; then
                log_warning "仍有标注平台进程存在，强制终止 (PIDs: $remaining_processes)..."
                for pid in $remaining_processes; do
                    kill -KILL $pid 2>/dev/null || true
                done
                sleep 2
            fi

            # 最终检查
            final_remaining=""
            for pid in $user_processes; do
                if kill -0 $pid 2>/dev/null; then
                    final_remaining="$final_remaining $pid"
                fi
            done

            if [ -z "$final_remaining" ]; then
                log_success "当前用户的所有标注平台Celery进程已成功清除"
            else
                log_warning "仍有标注平台Celery进程无法清除 (PIDs: $final_remaining)"
            fi
        else
            log_info "没有发现当前用户($current_user)的标注平台Celery进程"
        fi
    else
        log_info "没有发现在标注平台目录运行的Celery进程"
    fi

    # 最终检查所有celery进程（不干扰其他平台）
    final_check=$(pgrep -f 'celery worker' 2>/dev/null || true)
    if [ -n "$final_check" ]; then
        log_info "系统中仍有其他Celery进程在运行（可能是诊断平台等）:"
        ps aux | grep 'celery worker' | grep -v grep | grep -v "$SIMPLE_PATTERN" || true
        log_info "这些进程不会影响标注平台的启动，继续执行..."
    fi
else
    log_info "没有发现运行中的标注平台Celery进程"
fi

# 清理PID文件
if [ -f ./log/celery.pid ]; then
    rm -f ./log/celery.pid
    log_info "已清理旧的PID文件"
fi

# 4. 日志文件管理（混合轮转机制）
log_info "管理日志文件..."

# 检查当前日志文件大小并进行轮转
if [ -f ./log/celery.log ]; then
    # 获取文件大小（字节）
    log_size=$(stat -c%s ./log/celery.log 2>/dev/null || echo 0)
    log_size_mb=$(echo "scale=1; $log_size/1024/1024" | bc -l 2>/dev/null || echo "0")

    # 50MB = 52428800 字节
    max_size=52428800

    if [ "$log_size" -gt "$max_size" ]; then
        log_warning "当前日志文件过大 (${log_size_mb}MB > 50MB)，进行大小轮转..."
        timestamp=$(date '+%Y%m%d_%H%M%S')
        backup_file="./log/celery_${timestamp}_size.log"
        mv ./log/celery.log "$backup_file"
        log_info "已备份大文件: celery_${timestamp}_size.log"
    else
        # 每次启动都备份当前的日志文件（如果存在且不为空）
        if [ "$log_size" -gt 0 ]; then
            timestamp=$(date '+%Y%m%d_%H%M%S')
            backup_file="./log/celery_${timestamp}.log"
            mv ./log/celery.log "$backup_file"
            log_info "已备份当前日志文件: celery_${timestamp}.log (${log_size_mb}MB)"
        else
            log_info "当前日志文件为空，直接使用"
        fi
    fi
fi

# 清理旧的备份文件，只保留最新的10份
log_info "清理旧的日志备份文件..."
backup_count=$(ls -1 ./log/celery_*.log 2>/dev/null | wc -l)
if [ "$backup_count" -gt 10 ]; then
    # 删除最旧的备份文件，保留最新的10份
    deleted_count=0
    ls -1t ./log/celery_*.log | tail -n +11 | while read old_file; do
        rm -f "$old_file"
        log_info "已删除旧的日志备份: $(basename "$old_file")"
        deleted_count=$((deleted_count + 1))
    done
    log_success "日志备份清理完成，删除了 $((backup_count - 10)) 个文件，保留最新的10份"
else
    log_info "当前备份文件数量: $backup_count，无需清理"
fi

# 创建新的空日志文件
touch ./log/celery.log
log_success "已创建新的日志文件: ./log/celery.log (最大50MB)"

# 5. 启动新的celery worker
log_info "正在启动新的标注平台Celery Worker..."

# 构建唯一的worker名称，包含平台标识
WORKER_NAME="${PLATFORM_NAME}_worker@$(hostname)"
HOSTNAME_PREFIX="${PLATFORM_NAME}_$(hostname)"

log_info "Worker名称: $WORKER_NAME"
log_info "主机名前缀: $HOSTNAME_PREFIX"

# 后台启动celery并保存PID，只监听标注平台队列，坚决不监听诊断平台队列
nohup celery -A app.celery worker \
    --loglevel=INFO \
    --hostname="$HOSTNAME_PREFIX" \
    --logfile=./log/celery.log \
    --pidfile=./log/celery.pid \
    --time-limit=2400 \
    --soft-time-limit=1800 \
    --concurrency=10 \
    -n "$WORKER_NAME" \
    --queues=ailabel_queue \
    --pool=prefork \
    --max-tasks-per-child=50 \
    --without-gossip \
    --without-mingle \
    --without-heartbeat > /dev/null 2>&1 &

# 等待Celery启动并创建PID文件
log_info "等待Celery启动..."
sleep 2

# 6. 验证启动状态
log_info "验证Celery启动状态..."

# 等待PID文件创建，最多等待10秒
for i in {1..10}; do
    if [ -f ./log/celery.pid ]; then
        log_info "PID文件已创建 (等待${i}秒)"
        break
    fi
    log_info "等待PID文件创建... ($i/10)"
    sleep 1
done

if [ -f ./log/celery.pid ]; then
    celery_pid=$(cat ./log/celery.pid)
    log_info "从PID文件读取到进程ID: $celery_pid"

    # 验证进程是否存在
    if kill -0 "$celery_pid" 2>/dev/null; then
        log_success "Celery Worker启动成功! PID: $celery_pid"
        log_info "日志文件位置: ./log/celery.log"
        log_info "PID文件位置: ./log/celery.pid"

        # 显示celery状态
        echo ""
        log_info "Celery进程信息:"
        ps aux | grep "$celery_pid" | grep -v grep || true

        echo ""
        log_info "可以使用以下命令查看日志:"
        echo "  tail -f ./log/celery.log          # 查看实时日志"
        echo "  ls -lt ./log/celery_*.log         # 查看所有日志备份"
        echo ""
        log_info "可以使用以下命令停止Celery:"
        echo "  ./stop_celery.sh                  # 使用停止脚本"
        echo "  kill $celery_pid                  # 直接终止进程"
        echo "  pkill -f '$SIMPLE_PATTERN'        # 终止标注平台celery进程"

    else
        log_error "PID文件中的进程不存在，尝试备用验证..."
        # 备用验证：通过进程名称查找
        celery_processes=$(pgrep -f 'celery.*worker' 2>/dev/null || true)
        if [ -n "$celery_processes" ]; then
            log_warning "通过进程名称找到Celery进程，但PID文件可能有问题"
            echo "$celery_processes" | head -1 > ./log/celery.pid
            new_pid=$(cat ./log/celery.pid)
            log_success "已更新PID文件，Celery Worker运行中! PID: $new_pid"
        else
            log_error "Celery Worker启动失败 - 进程不存在"
            exit 1
        fi
    fi
else
    log_warning "未找到PID文件，尝试备用验证..."
    # 备用验证：通过进程名称查找
    celery_processes=$(pgrep -f 'celery.*worker' 2>/dev/null || true)
    if [ -n "$celery_processes" ]; then
        log_warning "通过进程名称找到Celery进程，PID文件可能创建失败"
        echo "$celery_processes" | head -1 > ./log/celery.pid
        backup_pid=$(cat ./log/celery.pid)
        log_success "已创建PID文件，Celery Worker运行中! PID: $backup_pid"
        log_info "日志文件位置: ./log/celery.log"
        log_info "PID文件位置: ./log/celery.pid"

        # 显示celery状态
        echo ""
        log_info "Celery进程信息:"
        ps aux | grep "$backup_pid" | grep -v grep || true

        echo ""
        log_info "可以使用以下命令查看日志:"
        echo "  tail -f ./log/celery.log          # 查看实时日志"
        echo "  ls -lt ./log/celery_*.log         # 查看所有日志备份"
        echo ""
        log_info "可以使用以下命令停止Celery:"
        echo "  ./stop_celery.sh                  # 使用停止脚本"
        echo "  kill $backup_pid                  # 直接终止进程"
        echo "  pkill -f '$SIMPLE_PATTERN'        # 终止标注平台celery进程"
    else
        log_error "Celery Worker启动失败 - 未找到运行中的进程"
        exit 1
    fi
fi

log_success "标注平台Celery启动脚本执行完成!"
