#!/bin/bash

# 标注平台开发环境Celery启动脚本
# Development Environment Celery Startup Script for aiLabel Platform

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] ERROR: $1${NC}"
}

log_info "开始启动标注平台开发环境Celery服务..."

# 环境配置
ENV_TYPE="dev"
PLATFORM_NAME="aiLabel"
CONDA_ENV="ailabel_env"
LOG_DIR="log/dev"
ENV_FILE=".env.dev"

log_info "环境类型: 开发环境 ($ENV_TYPE)"
log_info "平台标识: $PLATFORM_NAME"
log_info "Conda环境: $CONDA_ENV"
log_info "日志目录: $LOG_DIR"
log_info "配置文件: $ENV_FILE"

# 检查环境配置文件
if [ ! -f "$ENV_FILE" ]; then
    log_error "环境配置文件 $ENV_FILE 不存在"
    exit 1
fi

# 1. 激活conda环境
log_info "正在激活conda环境..."
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    source ~/miniconda3/etc/profile.d/conda.sh
    if conda activate $CONDA_ENV; then
        log_success "成功激活conda环境: $CONDA_ENV"
    else
        log_error "激活conda环境失败"
        exit 1
    fi
else
    log_error "找不到conda配置文件"
    exit 1
fi

# 2. 创建日志目录
log_info "创建日志目录..."
mkdir -p "$LOG_DIR"
if [ $? -eq 0 ]; then
    log_success "日志目录创建成功: $LOG_DIR"
else
    log_error "日志目录创建失败"
    exit 1
fi

# 3. 清除已存在的开发环境celery进程
log_info "正在清除已存在的标注平台开发环境Celery进程..."

current_user=$(whoami)
PROCESS_PATTERN="celery.*$CONDA_ENV.*aiLabel_python_backend.*dev"
SIMPLE_PATTERN="celery.*$CONDA_ENV.*dev"

log_info "进程识别模式: $PROCESS_PATTERN"

# 查找开发环境的celery进程
existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
if [ -n "$existing_processes" ]; then
    log_warning "发现开发环境Celery进程，正在清理..."
    for pid in $existing_processes; do
        proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
        proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
        if [ "$proc_user" = "$current_user" ] && [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
            log_info "停止开发环境进程 PID: $pid"
            kill -TERM $pid 2>/dev/null || true
        fi
    done
    sleep 3
    
    # 强制清理残留进程
    remaining=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
    if [ -n "$remaining" ]; then
        log_warning "强制清理残留的开发环境进程..."
        for pid in $remaining; do
            proc_cwd=$(readlink -f /proc/$pid/cwd 2>/dev/null || echo "")
            if [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]; then
                kill -KILL $pid 2>/dev/null || true
            fi
        done
    fi
    log_success "开发环境Celery进程清理完成"
else
    log_info "没有发现运行中的开发环境Celery进程"
fi

# 清理PID文件
if [ -f "$LOG_DIR/celery.pid" ]; then
    rm -f "$LOG_DIR/celery.pid"
    log_info "已清理旧的PID文件"
fi

# 4. 日志文件管理
log_info "管理日志文件..."

if [ -f "$LOG_DIR/celery.log" ]; then
    log_size=$(stat -c%s "$LOG_DIR/celery.log" 2>/dev/null || echo 0)
    max_size=52428800  # 50MB
    
    if [ "$log_size" -gt "$max_size" ]; then
        timestamp=$(date '+%Y%m%d_%H%M%S')
        backup_file="$LOG_DIR/celery_${timestamp}_size.log"
        mv "$LOG_DIR/celery.log" "$backup_file"
        log_info "已备份大文件: $backup_file"
    elif [ "$log_size" -gt 0 ]; then
        timestamp=$(date '+%Y%m%d_%H%M%S')
        backup_file="$LOG_DIR/celery_${timestamp}.log"
        mv "$LOG_DIR/celery.log" "$backup_file"
        log_info "已备份当前日志文件: $backup_file"
    fi
fi

# 清理旧的备份文件，只保留最新的5份
backup_count=$(ls -1 "$LOG_DIR"/celery_*.log 2>/dev/null | wc -l)
if [ "$backup_count" -gt 5 ]; then
    ls -1t "$LOG_DIR"/celery_*.log | tail -n +6 | while read old_file; do
        rm -f "$old_file"
        log_info "已删除旧的日志备份: $(basename "$old_file")"
    done
fi

# 创建新的日志文件并添加会话开始标记
touch "$LOG_DIR/celery.log"

# 添加会话开始标记到日志文件
{
    echo ""
    echo "================================================================================================"
    echo "🚀 NEW CELERY SESSION STARTED - $(date '+%Y-%m-%d %H:%M:%S')"
    echo "================================================================================================"
    echo "📋 Session Info:"
    echo "   Platform: 标注平台 (aiLabel Platform)"
    echo "   Environment: 开发环境 (Development)"
    echo "   Worker Name: aiLabel_dev_worker@$(hostname)"
    echo "   Queue: ailabel_queue"
    echo "   Config File: .env.dev"
    echo "   Log Level: DEBUG"
    echo "   Started By: $(whoami)"
    echo "   Process ID: Will be assigned after startup"
    echo "================================================================================================"
    echo ""
} >> "$LOG_DIR/celery.log"

log_success "已创建新的日志文件: $LOG_DIR/celery.log (包含会话标记)"

# 5. 启动新的celery worker
log_info "正在启动新的标注平台开发环境Celery Worker..."

WORKER_NAME="${PLATFORM_NAME}_${ENV_TYPE}_worker@$(hostname)"
HOSTNAME_PREFIX="${PLATFORM_NAME}_${ENV_TYPE}_$(hostname)"

log_info "Worker名称: $WORKER_NAME"
log_info "主机名前缀: $HOSTNAME_PREFIX"

# 设置环境变量，确保加载开发环境配置
export CELERY_ENV=dev

# 后台启动celery，只监听开发环境标注队列
# 使用env确保环境变量正确传递
nohup env CELERY_ENV=dev celery -A app.celery worker \
    --loglevel=DEBUG \
    --hostname="$HOSTNAME_PREFIX" \
    --logfile="$LOG_DIR/celery.log" \
    --pidfile="$LOG_DIR/celery.pid" \
    --time-limit=2400 \
    --soft-time-limit=1800 \
    --concurrency=8 \
    -n "$WORKER_NAME" \
    --queues=ailabel_queue \
    --pool=prefork \
    --max-tasks-per-child=30 \
    --without-gossip \
    --without-mingle \
    --without-heartbeat > /dev/null 2>&1 &

# 等待Celery启动
log_info "等待Celery启动..."
sleep 3

# 6. 验证启动状态
log_info "验证Celery启动状态..."

# 等待PID文件创建
for i in {1..10}; do
    if [ -f "$LOG_DIR/celery.pid" ]; then
        break
    fi
    log_info "等待PID文件创建... ($i/10)"
    sleep 1
done

if [ -f "$LOG_DIR/celery.pid" ]; then
    celery_pid=$(cat "$LOG_DIR/celery.pid")
    
    if kill -0 "$celery_pid" 2>/dev/null; then
        # 更新日志文件中的进程ID信息
        {
            echo "✅ CELERY WORKER STARTED SUCCESSFULLY - $(date '+%Y-%m-%d %H:%M:%S')"
            echo "   Process ID: $celery_pid"
            echo "   PID File: $LOG_DIR/celery.pid"
            echo "   Log File: $LOG_DIR/celery.log"
            echo "================================================================================================"
            echo ""
        } >> "$LOG_DIR/celery.log"

        log_success "标注平台开发环境Celery Worker启动成功! PID: $celery_pid"
        log_info "日志文件位置: $LOG_DIR/celery.log"
        log_info "PID文件位置: $LOG_DIR/celery.pid"
        log_info "环境配置: $ENV_FILE"
        
        echo ""
        log_info "可以使用以下命令查看日志:"
        echo "  tail -f $LOG_DIR/celery.log          # 查看实时日志"
        echo ""
        log_info "可以使用以下命令停止Celery:"
        echo "  ./stop_celery_dev.sh                 # 使用停止脚本"
        echo "  kill $celery_pid                     # 直接终止进程"
    else
        log_error "标注平台开发环境Celery Worker启动失败 - 进程不存在"
        exit 1
    fi
else
    log_error "标注平台开发环境Celery Worker启动失败 - 未找到PID文件"
    exit 1
fi

log_success "标注平台开发环境Celery启动脚本执行完成!"
