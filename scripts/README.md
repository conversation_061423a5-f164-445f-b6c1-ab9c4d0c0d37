# 脚本工具目录

本目录包含项目的各种脚本工具，按功能分类组织。

## 📁 目录结构

```
scripts/
├── celery/                 # Celery相关脚本
│   ├── start_celery.sh     # 通用Celery启动脚本
│   ├── start_celery_dev.sh # 开发环境Celery启动脚本
│   ├── start_celery_prod.sh # 生产环境Celery启动脚本
│   ├── stop_celery.sh      # 通用Celery停止脚本
│   ├── stop_celery_dev.sh  # 开发环境Celery停止脚本
│   └── stop_celery_prod.sh # 生产环境Celery停止脚本
├── deployment/             # 部署相关脚本（预留）
└── README.md               # 本文档
```

## 🚀 使用方法

### Celery 服务管理

#### 启动服务

```bash
# 开发环境
./scripts/celery/start_celery_dev.sh

# 生产环境
./scripts/celery/start_celery_prod.sh

# 通用启动（根据环境变量自动选择）
./scripts/celery/start_celery.sh
```

#### 停止服务

```bash
# 开发环境
./scripts/celery/stop_celery_dev.sh

# 生产环境
./scripts/celery/stop_celery_prod.sh

# 通用停止
./scripts/celery/stop_celery.sh
```

## 📝 脚本说明

### Celery 脚本特性

- **环境隔离**: 开发和生产环境使用不同的配置
- **进程管理**: 智能识别和管理Celery进程
- **日志轮转**: 自动管理日志文件大小和备份
- **错误处理**: 完善的错误检测和恢复机制
- **状态监控**: 启动后自动验证服务状态

### 安全特性

- **平台隔离**: 只操作标注平台的Celery进程，不影响其他平台
- **用户隔离**: 只管理当前用户的进程
- **目录识别**: 通过工作目录确保操作正确的项目

## 🔧 自定义配置

### 环境变量

脚本支持以下环境变量：

- `CELERY_ENV`: 指定环境类型（dev/prod）
- `CONDA_ENV`: 指定Conda环境名称
- `PLATFORM_NAME`: 平台标识名称

### 修改配置

如需修改脚本配置，请编辑对应的脚本文件中的配置部分：

```bash
# 在脚本中找到配置部分
ENV_TYPE="dev"
PLATFORM_NAME="aiLabel"
CONDA_ENV="ailabel_env"
LOG_DIR="log/dev"
```

## 📋 注意事项

1. **权限要求**: 确保脚本有执行权限
2. **环境依赖**: 需要安装Conda和相关Python环境
3. **日志管理**: 定期检查和清理日志文件
4. **进程监控**: 建议配合系统监控工具使用

## 🆘 故障排除

### 常见问题

1. **权限不足**
   ```bash
   chmod +x scripts/celery/*.sh
   ```

2. **Conda环境未找到**
   - 检查Conda是否正确安装
   - 确认环境名称是否正确

3. **端口冲突**
   - 检查RabbitMQ和Redis端口是否被占用
   - 确认防火墙设置

4. **日志文件过大**
   - 脚本会自动轮转日志文件
   - 可手动清理旧的备份文件

### 调试模式

在脚本中添加调试输出：

```bash
# 在脚本开头添加
set -x  # 启用调试模式
```

## 📞 支持

如遇到问题，请：

1. 查看脚本输出的错误信息
2. 检查日志文件内容
3. 确认环境配置是否正确
4. 联系开发团队获取支持
