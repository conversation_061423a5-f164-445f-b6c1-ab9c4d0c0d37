# 开发环境配置文件
# 数据库配置
MYSQL_HOST=**************
MYSQL_PORT=23307
MYSQL_DB=medlabel
MYSQL_USERNAME=admin
MYSQL_PASSWORD=vipa@404_admin

# Redis配置
REDIS_HOST=**************
REDIS_PORT=26380

# RabbitMQ配置
RABBITMQ_HOST=**************
RABBITMQ_PORT=25675
RABBITMQ_ACCOUNT=admin
RABBITMQ_PASSWORD=vipa@404

# RabbitMQ连接配置
RABBITMQ_CONNECTION_TIMEOUT=60
RABBITMQ_HEARTBEAT=300
RABBITMQ_SOCKET_TIMEOUT=10
RABBITMQ_BLOCKED_CONNECTION_TIMEOUT=300
RABBITMQ_CONNECTION_ATTEMPTS=3
RABBITMQ_RETRY_DELAY=1

# 应用配置
DEBUG_FLAG=true
HOST_UPLOADS=http://**************:3030/uploads

# 文件路径配置
PROJECT_SAVE_DIR=/nfs5/medlabel/medlabel_dev_yjb/projects

# 环境标识
ENVIRONMENT=dev
