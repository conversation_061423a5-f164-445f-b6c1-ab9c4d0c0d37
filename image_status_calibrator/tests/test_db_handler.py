#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
db_handler 模块的单元测试

测试 fetch_images_to_check 和 update_image_db_status 函数的各种场景
"""

import unittest
from unittest.mock import Mock
import mysql.connector

# 确保模块可以被找到
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from db_handler import fetch_images_to_check, update_image_db_status
from constants import STATUS_PENDING, STATUS_PROCESSING, STATUS_COMPLETED


class TestFetchImagesToCheck(unittest.TestCase):
    """测试 fetch_images_to_check 函数"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_cursor = Mock()
        
    def test_fetch_no_filters(self):
        """测试不提供任何过滤条件的查询"""
        # 设置模拟返回值
        expected_data = [
            (1, 101, 'image1.jpg', 1, 1),
            (2, 102, 'image2.jpg', 2, 1)
        ]
        self.mock_cursor.fetchall.return_value = expected_data
        
        # 执行函数
        result = fetch_images_to_check(self.mock_cursor)
        
        # 验证 execute 被正确调用
        self.mock_cursor.execute.assert_called_once()
        call_args = self.mock_cursor.execute.call_args
        
        # 检查 SQL 查询
        sql_query = call_args[0][0]
        params = call_args[0][1]
        
        # 断言 SQL 不包含额外的 AND 条件
        self.assertNotIn('AND p.projectId', sql_query)
        self.assertNotIn('AND i.imageGroupId', sql_query)
        
        # 断言参数只包含状态值
        expected_params = [STATUS_PENDING, STATUS_PROCESSING]
        self.assertEqual(params, expected_params)
        
        # 断言返回结果正确
        self.assertEqual(result, expected_data)
        
    def test_fetch_with_project_id(self):
        """测试提供 project_id 过滤条件的查询"""
        project_id = 123
        expected_data = [(1, 123, 'image1.jpg', 1, 1)]
        self.mock_cursor.fetchall.return_value = expected_data
        
        # 执行函数
        result = fetch_images_to_check(self.mock_cursor, project_id_filter=project_id)
        
        # 验证 execute 被正确调用
        self.mock_cursor.execute.assert_called_once()
        call_args = self.mock_cursor.execute.call_args
        
        # 检查 SQL 查询和参数
        sql_query = call_args[0][0]
        params = call_args[0][1]
        
        # 断言 SQL 包含项目 ID 条件
        self.assertIn('AND p.projectId = %s', sql_query)
        
        # 断言参数包含项目 ID
        expected_params = [STATUS_PENDING, STATUS_PROCESSING, project_id]
        self.assertEqual(params, expected_params)
        
        # 断言返回结果正确
        self.assertEqual(result, expected_data)
        
    def test_fetch_with_group_id(self):
        """测试提供 group_id 过滤条件的查询"""
        group_id = 456
        expected_data = [(1, 101, 'image1.jpg', 1, 1)]
        self.mock_cursor.fetchall.return_value = expected_data
        
        # 执行函数
        result = fetch_images_to_check(self.mock_cursor, group_id_filter=group_id)
        
        # 验证 execute 被正确调用
        self.mock_cursor.execute.assert_called_once()
        call_args = self.mock_cursor.execute.call_args
        
        # 检查 SQL 查询和参数
        sql_query = call_args[0][0]
        params = call_args[0][1]
        
        # 断言 SQL 包含图像组 ID 条件
        self.assertIn('AND i.imageGroupId = %s', sql_query)
        
        # 断言参数包含图像组 ID
        expected_params = [STATUS_PENDING, STATUS_PROCESSING, group_id]
        self.assertEqual(params, expected_params)
        
        # 断言返回结果正确
        self.assertEqual(result, expected_data)
        
    def test_fetch_with_both_ids_group_has_priority(self):
        """测试同时提供 project_id 和 group_id，group_id 优先"""
        project_id = 123
        group_id = 456
        expected_data = [(1, 123, 'image1.jpg', 1, 1)]
        self.mock_cursor.fetchall.return_value = expected_data
        
        # 执行函数
        result = fetch_images_to_check(
            self.mock_cursor, 
            project_id_filter=project_id, 
            group_id_filter=group_id
        )
        
        # 验证 execute 被正确调用
        self.mock_cursor.execute.assert_called_once()
        call_args = self.mock_cursor.execute.call_args
        
        # 检查 SQL 查询和参数
        sql_query = call_args[0][0]
        params = call_args[0][1]
        
        # 断言 SQL 只包含图像组 ID 条件（根据实现逻辑，group_id 优先）
        self.assertIn('AND i.imageGroupId = %s', sql_query)
        self.assertNotIn('AND p.projectId = %s', sql_query)
        
        # 断言参数包含图像组 ID 而不是项目 ID
        expected_params = [STATUS_PENDING, STATUS_PROCESSING, group_id]
        self.assertEqual(params, expected_params)
        
        # 断言返回结果正确
        self.assertEqual(result, expected_data)
        
    def test_returns_fetched_data(self):
        """测试函数正确返回查询到的数据"""
        # 设置模拟数据
        mock_data = [
            (1, 101, 'test1.jpg', 1, 1),
            (2, 102, 'test2.jpg', 2, 2),
            (3, 103, 'test3.jpg', 3, 1)
        ]
        self.mock_cursor.fetchall.return_value = mock_data
        
        # 执行函数
        result = fetch_images_to_check(self.mock_cursor)
        
        # 断言返回的数据与模拟数据完全一致
        self.assertEqual(result, mock_data)


class TestUpdateImageDbStatus(unittest.TestCase):
    """测试 update_image_db_status 函数"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_cursor = Mock()
        
    def test_update_status_success(self):
        """测试成功更新状态"""
        # 设置测试参数
        image_id = 123
        new_status = STATUS_COMPLETED
        current_version = 5
        
        # 模拟成功更新（受影响行数为 1）
        self.mock_cursor.rowcount = 1
        
        # 执行函数
        result = update_image_db_status(self.mock_cursor, image_id, new_status, current_version)
        
        # 验证 execute 被正确调用
        self.mock_cursor.execute.assert_called_once()
        call_args = self.mock_cursor.execute.call_args
        
        # 检查 SQL 语句和参数
        sql_query = call_args[0][0]
        params = call_args[0][1]
        
        # 断言 SQL 是正确的 UPDATE 语句
        self.assertIn('UPDATE Image', sql_query)
        self.assertIn('SET status = %s, version = %s, updatedTime = NOW()', sql_query)
        self.assertIn('WHERE imageId = %s AND version = %s', sql_query)
        
        # 断言参数正确
        expected_params = (new_status, current_version + 1, image_id, current_version)
        self.assertEqual(params, expected_params)
        
        # 断言函数返回 True
        self.assertTrue(result)
        
    def test_update_status_version_mismatch(self):
        """测试版本不匹配导致更新失败"""
        # 设置测试参数
        image_id = 123
        new_status = STATUS_COMPLETED
        current_version = 5
        
        # 模拟版本不匹配（受影响行数为 0）
        self.mock_cursor.rowcount = 0
        
        # 执行函数
        result = update_image_db_status(self.mock_cursor, image_id, new_status, current_version)
        
        # 验证 execute 被调用
        self.mock_cursor.execute.assert_called_once()
        
        # 断言函数返回 False
        self.assertFalse(result)
        
    def test_update_status_db_error(self):
        """测试数据库错误导致更新失败"""
        # 设置测试参数
        image_id = 123
        new_status = STATUS_COMPLETED
        current_version = 5
        
        # 模拟数据库错误
        self.mock_cursor.execute.side_effect = mysql.connector.Error("Database connection failed")
        
        # 执行函数
        result = update_image_db_status(self.mock_cursor, image_id, new_status, current_version)
        
        # 验证 execute 被调用
        self.mock_cursor.execute.assert_called_once()
        
        # 断言函数返回 False
        self.assertFalse(result)


if __name__ == '__main__':
    unittest.main()