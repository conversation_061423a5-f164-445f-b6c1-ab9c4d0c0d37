#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nfs_checker 模块的单元测试
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import sys

# 将 nfs_checker.py 所在的目录添加到 sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from nfs_checker import (
    check_type1_2_status,
    check_type3_status,
    check_type4_status,
    check_nfs_status
)


class TestCheckType1_2Status(unittest.TestCase):
    """测试 check_type1_2_status 函数"""

    @patch('nfs_checker.os.path.exists')
    def test_success(self, mock_exists):
        """测试主图片和缩略图都存在的成功情况"""
        # 模拟主图片和缩略图都存在
        mock_exists.return_value = True
        
        result = check_type1_2_status("/test/project", "test_image")
        
        self.assertTrue(result)
        # 验证 os.path.exists 被调用了两次（主图片和缩略图）
        self.assertEqual(mock_exists.call_count, 2)

    @patch('nfs_checker.os.path.exists')
    def test_missing_main_image(self, mock_exists):
        """测试主图片不存在的情况"""
        # 第一次调用（主图片）返回 False，第二次调用（缩略图）返回 True
        mock_exists.side_effect = [False, True]
        
        result = check_type1_2_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    def test_missing_thumbnail(self, mock_exists):
        """测试缩略图不存在的情况"""
        # 第一次调用（主图片）返回 True，第二次调用（缩略图）返回 False
        mock_exists.side_effect = [True, False]
        
        result = check_type1_2_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    def test_both_missing(self, mock_exists):
        """测试主图片和缩略图都不存在的情况"""
        mock_exists.return_value = False
        
        result = check_type1_2_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    def test_exception_handling(self, mock_exists):
        """测试异常处理"""
        # 模拟 os.path.exists 抛出异常
        mock_exists.side_effect = Exception("文件系统错误")
        
        result = check_type1_2_status("/test/project", "test_image")
        
        self.assertFalse(result)


class TestCheckType3Status(unittest.TestCase):
    """测试 check_type3_status 函数"""

    @patch('nfs_checker.os.path.exists')
    def test_success(self, mock_exists):
        """测试 metadata.xml 存在的成功情况"""
        mock_exists.return_value = True
        
        result = check_type3_status("/test/project", "test_image")
        
        self.assertTrue(result)
        # 验证正确的路径被检查
        expected_path = "/test/project/test_image/deepzoom/metadata.xml"
        mock_exists.assert_called_once_with(expected_path)

    @patch('nfs_checker.os.path.exists')
    def test_failure(self, mock_exists):
        """测试 metadata.xml 不存在的情况"""
        mock_exists.return_value = False
        
        result = check_type3_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    def test_exception_handling(self, mock_exists):
        """测试异常处理"""
        mock_exists.side_effect = Exception("文件系统错误")
        
        result = check_type3_status("/test/project", "test_image")
        
        self.assertFalse(result)


class TestCheckType4Status(unittest.TestCase):
    """测试 check_type4_status 函数（严格逻辑）"""

    @patch('nfs_checker.os.path.exists')
    @patch('nfs_checker.glob.glob')
    def test_success_single_channel(self, mock_glob, mock_exists):
        """测试单个通道文件成功的情况"""
        # 模拟找到一个通道文件
        mock_glob.return_value = ['/test/project/test_image/split/channel_1.tiff']
        # 模拟对应的 metadata.xml 存在
        mock_exists.return_value = True
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertTrue(result)
        # 验证正确的 metadata.xml 路径被检查
        expected_metadata_path = "/test/project/test_image/1/deepzoom/metadata.xml"
        mock_exists.assert_called_once_with(expected_metadata_path)

    @patch('nfs_checker.os.path.exists')
    @patch('nfs_checker.glob.glob')
    def test_success_multiple_channels(self, mock_glob, mock_exists):
        """测试多个通道文件成功的情况"""
        # 模拟找到两个通道文件
        mock_glob.return_value = [
            '/test/project/test_image/split/channel_1.tiff',
            '/test/project/test_image/split/channel_2.tiff'
        ]
        # 模拟所有对应的 metadata.xml 都存在
        mock_exists.return_value = True
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertTrue(result)
        # 验证两个 metadata.xml 路径都被检查
        self.assertEqual(mock_exists.call_count, 2)

    @patch('nfs_checker.glob.glob')
    def test_failure_no_channels_found(self, mock_glob):
        """测试未找到通道文件的情况"""
        # 模拟没有找到任何通道文件
        mock_glob.return_value = []
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    @patch('nfs_checker.glob.glob')
    def test_failure_partial_metadata(self, mock_glob, mock_exists):
        """测试部分 metadata.xml 不存在的情况"""
        # 模拟找到两个通道文件
        mock_glob.return_value = [
            '/test/project/test_image/split/channel_1.tiff',
            '/test/project/test_image/split/channel_2.tiff'
        ]
        # 模拟只有第一个通道的 metadata.xml 存在
        mock_exists.side_effect = [True, False]
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    @patch('nfs_checker.glob.glob')
    def test_failure_invalid_channel_filename(self, mock_glob, mock_exists):
        """测试无效通道文件名的情况"""
        # 模拟找到一个无效格式的文件名
        mock_glob.return_value = ['/test/project/test_image/split/invalid_file.tiff']
        # 对于无效文件名，对应的 metadata.xml 不存在
        mock_exists.return_value = False
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.os.path.exists')
    @patch('nfs_checker.glob.glob')
    def test_failure_all_metadata_missing(self, mock_glob, mock_exists):
        """测试所有 metadata.xml 都不存在的情况"""
        # 模拟找到两个通道文件
        mock_glob.return_value = [
            '/test/project/test_image/split/channel_1.tiff',
            '/test/project/test_image/split/channel_2.tiff'
        ]
        # 模拟所有 metadata.xml 都不存在
        mock_exists.return_value = False
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertFalse(result)

    @patch('nfs_checker.glob.glob')
    def test_exception_handling(self, mock_glob):
        """测试异常处理"""
        # 模拟 glob.glob 抛出异常
        mock_glob.side_effect = Exception("文件系统错误")
        
        result = check_type4_status("/test/project", "test_image")
        
        self.assertFalse(result)


class TestCheckNfsStatus(unittest.TestCase):
    """测试 check_nfs_status 分发函数"""

    @patch('nfs_checker.check_type1_2_status')
    def test_dispatches_to_type1(self, mock_check_type1_2):
        """测试分发到 type1 检查函数"""
        mock_check_type1_2.return_value = True
        
        result = check_nfs_status("/nfs/base", 123, "test_image", 1)
        
        self.assertTrue(result)
        mock_check_type1_2.assert_called_once_with("/nfs/base/123", "test_image")

    @patch('nfs_checker.check_type1_2_status')
    def test_dispatches_to_type2(self, mock_check_type1_2):
        """测试分发到 type2 检查函数"""
        mock_check_type1_2.return_value = False
        
        result = check_nfs_status("/nfs/base", 123, "test_image", 2)
        
        self.assertFalse(result)
        mock_check_type1_2.assert_called_once_with("/nfs/base/123", "test_image")

    @patch('nfs_checker.check_type3_status')
    def test_dispatches_to_type3(self, mock_check_type3):
        """测试分发到 type3 检查函数"""
        mock_check_type3.return_value = True
        
        result = check_nfs_status("/nfs/base", 456, "test_image", 3)
        
        self.assertTrue(result)
        mock_check_type3.assert_called_once_with("/nfs/base/456", "test_image")

    @patch('nfs_checker.check_type4_status')
    def test_dispatches_to_type4(self, mock_check_type4):
        """测试分发到 type4 检查函数"""
        mock_check_type4.return_value = False
        
        result = check_nfs_status("/nfs/base", 789, "test_image", 4)
        
        self.assertFalse(result)
        mock_check_type4.assert_called_once_with("/nfs/base/789", "test_image")

    def test_unknown_type(self):
        """测试未知图片类型的情况"""
        result = check_nfs_status("/nfs/base", 123, "test_image", 99)
        
        self.assertFalse(result)

    @patch('nfs_checker.check_type1_2_status')
    def test_exception_handling(self, mock_check_type1_2):
        """测试异常处理"""
        # 模拟检查函数抛出异常
        mock_check_type1_2.side_effect = Exception("检查错误")
        
        result = check_nfs_status("/nfs/base", 123, "test_image", 1)
        
        self.assertFalse(result)

    def test_path_construction(self):
        """测试项目路径构建的正确性"""
        # 使用一个不存在的类型来避免实际调用检查函数，但仍能测试路径构建
        result = check_nfs_status("/nfs/base", 12345, "test_image", 99)
        
        # 虽然结果是 False（因为未知类型），但不会因为路径问题抛出异常
        self.assertFalse(result)


if __name__ == '__main__':
    unittest.main()