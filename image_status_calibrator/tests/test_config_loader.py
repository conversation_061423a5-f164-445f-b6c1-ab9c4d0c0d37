#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
config_loader 模块的单元测试

测试配置加载和验证功能。
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import shutil

# 添加父目录到 Python 路径，以便导入 config_loader 模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config_loader import load_config


class TestConfigLoader(unittest.TestCase):
    """config_loader 模块的测试类"""

    def setUp(self):
        """测试前的设置"""
        # 创建临时目录用于测试
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)

    def tearDown(self):
        """测试后的清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)

    def test_load_config_success(self):
        """测试配置加载成功的情况"""
        # 模拟所有必要的环境变量
        test_env = {
            'DB_HOST': 'test_host',
            'DB_PORT': '12345',
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    config = load_config()
                    
                    # 验证返回的配置字典
                    expected_config = {
                        'DB_HOST': 'test_host',
                        'DB_PORT': 12345,  # 应该被转换为整数
                        'DB_USER': 'test_user',
                        'DB_PASSWORD': 'test_password',
                        'DB_NAME': 'test_db',
                        'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
                    }
                    
                    self.assertEqual(config, expected_config)
                    self.assertIsInstance(config['DB_PORT'], int)

    def test_load_config_missing_required_field(self):
        """测试缺少必要配置项的情况"""
        # 模拟缺少 DB_HOST 配置项
        test_env = {
            'DB_PORT': '12345',
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
            # 故意省略 DB_HOST
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()

    def test_load_config_empty_required_field(self):
        """测试必要配置项为空字符串的情况"""
        # 模拟 DB_HOST 为空字符串
        test_env = {
            'DB_HOST': '',  # 空字符串
            'DB_PORT': '12345',
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()

    def test_load_config_whitespace_only_field(self):
        """测试必要配置项只包含空白字符的情况"""
        # 模拟 DB_HOST 只包含空白字符
        test_env = {
            'DB_HOST': '   ',  # 只有空格
            'DB_PORT': '12345',
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()

    def test_load_config_invalid_port(self):
        """测试 DB_PORT 不是有效数字的情况"""
        # 模拟 DB_PORT 为非数字字符串
        test_env = {
            'DB_HOST': 'test_host',
            'DB_PORT': 'invalid_port',  # 非数字
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()

    def test_load_config_port_as_float(self):
        """测试 DB_PORT 为浮点数的情况"""
        # 模拟 DB_PORT 为浮点数字符串
        test_env = {
            'DB_HOST': 'test_host',
            'DB_PORT': '12345.5',  # 浮点数
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/projects'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)，因为 int() 无法处理浮点数字符串
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()

    def test_load_config_multiple_missing_fields(self):
        """测试缺少多个必要配置项的情况"""
        # 模拟缺少多个配置项
        test_env = {
            'DB_PORT': '12345',
            'DB_USER': 'test_user',
            # 故意省略 DB_HOST, DB_PASSWORD, DB_NAME, NFS_PROJECT_SAVE_DIR
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            with patch('config_loader.find_dotenv', return_value='.env'):
                with patch('config_loader.load_dotenv'):
                    with patch('config_loader.logging') as mock_logging:
                        # 断言会调用 sys.exit(1)
                        with self.assertRaises(SystemExit) as cm:
                            load_config()
                        
                        self.assertEqual(cm.exception.code, 1)
                        # 验证是否记录了错误日志
                        mock_logging.error.assert_called()


if __name__ == '__main__':
    unittest.main()