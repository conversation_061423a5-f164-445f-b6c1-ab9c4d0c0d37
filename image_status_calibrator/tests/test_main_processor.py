import sys
import os
import unittest
from unittest.mock import patch, Mock, call
import argparse

# Ensure the project root is in the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from image_status_calibrator.main_processor import main
from image_status_calibrator.constants import STATUS_COMPLETED, STATUS_ERROR

class TestMainProcessorIntegration(unittest.TestCase):
    """
    Integration tests for the main_processor module.
    These tests focus on the interaction between main_processor and its dependencies.
    """

    def setUp(self):
        """Set up common mock objects for tests."""
        self.mock_app_config = {
            'DB_HOST': 'test_host',
            'DB_PORT': 3306,
            'DB_USER': 'test_user',
            'DB_PASSWORD': 'test_password',
            'DB_NAME': 'test_db',
            'NFS_PROJECT_SAVE_DIR': '/test/nfs/base'
        }
        self.mock_args = argparse.Namespace(project_id=None, group_id=None)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status')
    @patch('image_status_calibrator.main_processor.check_nfs_status')
    @patch('image_status_calibrator.main_processor.fetch_images_to_check', return_value=[])
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_no_images_found(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when no images are found to process."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        db_config_arg = {
            'host': self.mock_app_config['DB_HOST'],
            'port': self.mock_app_config['DB_PORT'],
            'user': self.mock_app_config['DB_USER'],
            'password': self.mock_app_config['DB_PASSWORD'],
            'database': self.mock_app_config['DB_NAME']
        }

        # Act
        main()

        # Assert
        mock_parse_args.assert_called_once()
        mock_load_config.assert_called_once()
        mock_connect_db.assert_called_once_with(db_config_arg)
        mock_fetch_images.assert_called_once_with(mock_cursor, None, None)
        
        mock_check_nfs.assert_not_called()
        mock_update_status.assert_not_called()
        mock_conn.commit.assert_not_called()
        mock_conn.rollback.assert_not_called()
        
        mock_logging.info.assert_any_call("数据库查询和记录遍历完成")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 0 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status', return_value=True)
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=True)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_nfs_complete_db_update_success(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow for one image with successful NFS check and DB update."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (1, 101, 'img1.png', 1, 0) # imageId, projectId, imageName, imageTypeId, version
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_fetch_images.assert_called_once()
        mock_check_nfs.assert_called_once_with(
            self.mock_app_config['NFS_PROJECT_SAVE_DIR'],
            image_record[1],
            image_record[2],
            image_record[3]
        )
        mock_update_status.assert_called_once_with(
            mock_cursor,
            image_record[0],
            STATUS_COMPLETED,
            image_record[4]
        )
        mock_conn.commit.assert_called_once()
        mock_logging.info.assert_any_call(f"Image ID: {image_record[0]} - Successfully updated status to COMPLETED in DB.")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 1 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)


    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status', return_value=False)
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=True)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_completed(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when DB update fails (e.g., version mismatch)."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (2, 102, 'img2.png', 2, 1)
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_check_nfs.assert_called_once()
        mock_update_status.assert_called_once()
        mock_conn.rollback.assert_not_called() # The main loop continues, no rollback on single failure
        mock_conn.commit.assert_not_called()
        mock_logging.warning.assert_called_once_with(
            f"Image ID: {image_record[0]} - Failed to update status to COMPLETED in DB (possibly due to version mismatch or record not found)."
        )
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status', return_value=True)
    @patch('image_status_calibrator.main_processor.should_mark_as_error', return_value=True)
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=False)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_error(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_should_error, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when an image should be marked as error."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (3, 103, 'img3.png', 3, 0)
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_check_nfs.assert_called_once_with(
            self.mock_app_config['NFS_PROJECT_SAVE_DIR'],
            image_record[1],
            image_record[2],
            image_record[3]
        )
        mock_should_error.assert_called_once_with(
            self.mock_app_config['NFS_PROJECT_SAVE_DIR'],
            image_record[1],
            image_record[2],
            image_record[3]
        )
        mock_update_status.assert_called_once_with(
            mock_cursor,
            image_record[0],
            STATUS_ERROR,
            image_record[4]
        )
        mock_conn.commit.assert_called_once()
        mock_logging.info.assert_any_call(f"Image ID: {image_record[0]} - Successfully updated status to ERROR in DB due to incomplete files.")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 0 images. Updated to ERROR: 1 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status')
    @patch('image_status_calibrator.main_processor.should_mark_as_error', return_value=False)
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=False)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_no_update_needed(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_should_error, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when no update is needed (NFS not complete but no error condition)."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (4, 104, 'img4.png', 4, 0)
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_check_nfs.assert_called_once()
        mock_should_error.assert_called_once()
        mock_update_status.assert_not_called()
        mock_conn.commit.assert_not_called()
        mock_conn.rollback.assert_not_called()
        mock_logging.info.assert_any_call(f"Image ID: {image_record[0]} - NFS status is NOT COMPLETED, but no error condition detected. DB status will not be updated.")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status')
    @patch('image_status_calibrator.main_processor.should_mark_as_error', return_value=False)
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=False)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_nfs_not_complete(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_should_error, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when the NFS check for an image fails."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (3, 103, 'img3.png', 3, 2)
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_check_nfs.assert_called_once()
        mock_should_error.assert_called_once()
        mock_update_status.assert_not_called()
        mock_conn.commit.assert_not_called()
        mock_conn.rollback.assert_not_called()
        mock_logging.info.assert_any_call(f"Image ID: {image_record[0]} - NFS status is NOT COMPLETED, but no error condition detected. DB status will not be updated.")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.update_image_db_status')
    @patch('image_status_calibrator.main_processor.check_nfs_status', return_value=False)
    @patch('image_status_calibrator.main_processor.fetch_images_to_check')
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_one_image_nfs_not_complete(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_check_nfs, mock_update_status, mock_close_db, mock_logging
    ):
        """Test the main flow when the NFS check for an image fails."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)
        image_record = (3, 103, 'img3.png', 3, 2)
        mock_fetch_images.return_value = [image_record]

        # Act
        main()

        # Assert
        mock_check_nfs.assert_called_once()
        mock_update_status.assert_not_called()
        mock_conn.commit.assert_not_called()
        mock_conn.rollback.assert_not_called()
        mock_logging.info.assert_any_call(f"Image ID: {image_record[0]} - NFS status is NOT COMPLETED, but no error condition detected. DB status will not be updated.")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 1 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('image_status_calibrator.main_processor.close_db')
    @patch('image_status_calibrator.main_processor.fetch_images_to_check', return_value=[])
    @patch('image_status_calibrator.main_processor.connect_db')
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_flow_with_project_id_filter(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_fetch_images, mock_close_db, mock_logging
    ):
        """Test that the project_id filter is passed to fetch_images_to_check."""
        # Arrange
        filtered_args = argparse.Namespace(project_id=101, group_id=None)
        mock_parse_args.return_value = filtered_args
        mock_load_config.return_value = self.mock_app_config
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect_db.return_value = (mock_conn, mock_cursor)

        # Act
        main()

        # Assert
        mock_fetch_images.assert_called_once_with(mock_cursor, 101, None)
        mock_logging.info.assert_any_call("命令行参数:")
        mock_logging.info.assert_any_call(f"  Project ID filter: 101")
        mock_logging.info.assert_any_call("Calibration finished. Checked: 0 images. Updated to COMPLETED: 0 images. Updated to ERROR: 0 images.")
        mock_close_db.assert_called_once_with(mock_conn, mock_cursor)

    @patch('image_status_calibrator.main_processor.logging')
    @patch('sys.exit')
    @patch('image_status_calibrator.main_processor.connect_db', return_value=(None, None))
    @patch('image_status_calibrator.main_processor.load_config')
    @patch('image_status_calibrator.main_processor.parse_arguments')
    def test_main_db_connection_fails(
        self, mock_parse_args, mock_load_config, mock_connect_db,
        mock_exit, mock_logging
    ):
        """Test the script exits if the database connection fails."""
        # Arrange
        mock_parse_args.return_value = self.mock_args
        mock_load_config.return_value = self.mock_app_config

        # Act
        main()

        # Assert
        mock_connect_db.assert_called_once()
        mock_logging.error.assert_any_call("数据库连接失败，程序退出")
        mock_exit.assert_called_with(1)

if __name__ == '__main__':
    unittest.main(verbosity=2)