#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库处理模块

负责数据库连接、查询和更新操作。
支持PostgreSQL数据库，使用专用的image_status_updater用户。
"""

import logging
import psycopg2
import psycopg2.extras
from constants import STATUS_PENDING, STATUS_PROCESSING, STATUS_COMPLETED


def connect_db(db_config):
    """
    建立PostgreSQL数据库连接

    Args:
        db_config (dict): 包含数据库连接参数的字典
                         必须包含: host, port, user, password, database

    Returns:
        tuple: (connection, cursor) 数据库连接对象和游标对象
               如果连接失败，返回 (None, None)
    """
    connection = None
    cursor = None

    try:
        logging.info("正在连接到PostgreSQL数据库...")
        connection = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database']
        )

        # 设置自动提交模式
        connection.autocommit = True

        logging.info("PostgreSQL数据库连接成功")
        cursor = connection.cursor(cursor_factory=psycopg2.extras.DictCursor)
        return connection, cursor

    except psycopg2.Error as db_error:
        logging.error(f"PostgreSQL数据库连接失败: {db_error}")
        if connection:
            connection.close()
        return None, None
    except Exception as e:
        logging.error(f"建立数据库连接时发生未预期的错误: {e}")
        if connection:
            connection.close()
        return None, None


def close_db(connection, cursor):
    """
    安全地关闭PostgreSQL数据库连接和游标

    Args:
        connection: 数据库连接对象
        cursor: 数据库游标对象
    """
    try:
        if cursor:
            cursor.close()
            logging.debug("数据库游标已关闭")
        if connection and not connection.closed:
            connection.close()
            logging.info("PostgreSQL数据库连接已关闭")
    except Exception as e:
        logging.error(f"关闭数据库连接时发生错误: {e}")


def fetch_images_to_check(cursor, project_id_filter=None, group_id_filter=None):
    """
    查询需要检查的图像记录

    Args:
        cursor: 数据库游标对象
        project_id_filter (int, optional): 项目 ID 筛选条件
        group_id_filter (int, optional): 图像组 ID 筛选条件

    Returns:
        list: 查询结果列表，每个元素包含 (image_id, project_id, image_hash, image_type_id, version)

    Raises:
        psycopg2.Error: 数据库查询错误
    """
    try:
        # 构建基础 SQL 查询语句 (使用PostgreSQL表名)
        base_query = """
        SELECT i.image_id, p.project_id, i.image_hash, i.image_type_id, i.version
        FROM images i
        JOIN image_groups ig ON i.image_group_id = ig.image_group_id
        JOIN projects p ON ig.project_id = p.project_id
        WHERE i.status IN (%s, %s)
        """

        # 准备参数列表
        params = [STATUS_PENDING, STATUS_PROCESSING]

        # 根据筛选条件动态修改查询语句
        query_conditions = []
        filter_description = "所有项目"

        if group_id_filter is not None:
            query_conditions.append("AND i.image_group_id = %s")
            params.append(group_id_filter)
            filter_description = f"图像组 ID: {group_id_filter}"
        elif project_id_filter is not None:
            query_conditions.append("AND p.project_id = %s")
            params.append(project_id_filter)
            filter_description = f"项目 ID: {project_id_filter}"

        # 构建最终查询语句
        final_query = base_query + " ".join(query_conditions)

        logging.info(f"查询范围: {filter_description}")
        logging.debug(f"执行 SQL 查询: {final_query}")
        logging.debug(f"查询参数: {params}")

        # 执行查询
        cursor.execute(final_query, params)
        images_to_check = cursor.fetchall()

        logging.info(f"找到 {len(images_to_check)} 条状态为 PENDING 或 PROCESSING 的图像记录需要检查")

        return images_to_check

    except psycopg2.Error as db_error:
        logging.error(f"PostgreSQL数据库查询失败: {db_error}")
        raise
    except Exception as e:
        logging.error(f"查询图像记录时发生未预期的错误: {e}")
        raise


def update_image_db_status(cursor, image_id, new_status, current_version):
    """
    更新图像的数据库状态，使用专用的PostgreSQL函数和乐观锁确保数据一致性

    Args:
        cursor: 数据库游标对象
        image_id (int): 图像 ID
        new_status (int): 新的状态值 (应为 STATUS_COMPLETED)
        current_version (int): 当前版本号（用于乐观锁）

    Returns:
        bool: 更新成功返回 True，失败返回 False
    """
    try:
        # 使用专用的update_image_status函数进行安全更新
        update_sql = "SELECT update_image_status(%s, %s, %s)"

        # 执行更新语句
        params = (image_id, new_status, current_version)

        logging.debug(f"执行数据库更新 - Image ID: {image_id}, 新状态: {new_status}, "
                     f"当前版本: {current_version}")

        cursor.execute(update_sql, params)
        result = cursor.fetchone()

        # 检查函数返回结果
        if result and result[0]:
            logging.debug(f"Image ID: {image_id} - 数据库状态更新成功")
            return True
        else:
            # 可能由于并发修改导致 version 不匹配，或者 image_id 不存在
            logging.warning(f"Image ID: {image_id} - 数据库状态更新失败 "
                          f"(可能由于版本冲突或记录不存在)")
            return False

    except psycopg2.Error as db_error:
        logging.error(f"Image ID: {image_id} - PostgreSQL数据库更新操作失败: {db_error}")
        return False
    except Exception as e:
        logging.error(f"Image ID: {image_id} - 更新数据库状态时发生未预期的错误: {e}")
        return False