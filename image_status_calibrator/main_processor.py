#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像状态校准脚本

用于根据 NFS 文件系统的状态校准 PostgreSQL 数据库中 images 表的状态。
支持通过 .env 文件配置，并能按 project_id 或 image_group_id 筛选检测范围。
使用专用的 image_status_updater 数据库用户进行安全的状态更新操作。
"""

import sys
import os
import argparse
import logging
from config_loader import load_config
from db_handler import connect_db, close_db, fetch_images_to_check, update_image_db_status
from nfs_checker import check_nfs_status
from constants import STATUS_PENDING, STATUS_PROCESSING, STATUS_COMPLETED, STATUS_ERROR


def setup_logging():
    """设置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='图像状态校准脚本 - 根据 NFS 文件系统状态校准数据库中的图像状态',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--project-id',
        type=int,
        help='指定项目 ID 来筛选图像范围'
    )
    
    parser.add_argument(
        '--group-id', 
        type=int,
        help='指定图像组 ID 来筛选图像范围'
    )
    
    return parser.parse_args()


def should_mark_as_error(nfs_base_path, project_id, image_name, image_type_id):
    """
    判断是否应该将图片状态标记为 ERROR
    
    Args:
        nfs_base_path (str): NFS 基础路径
        project_id (int): 项目 ID
        image_name (str): 图片名称
        image_type_id (int): 图片类型 ID
        
    Returns:
        bool: True 表示应标记为 ERROR，False 表示不应标记为 ERROR
    """
    try:
        project_path = os.path.join(nfs_base_path, str(project_id))
        
        if image_type_id in [1, 2]:  # Type 1/2
            # 检查主图片文件和缩略图是否部分存在但不完整
            main_image_path = os.path.join(project_path, f"{image_name}.png")
            thumbnail_path = os.path.join(project_path, "thumbnail", f"{image_name}.png")
            
            main_exists = os.path.exists(main_image_path)
            thumbnail_exists = os.path.exists(thumbnail_path)
            
            # 如果主图片存在但缩略图缺失，或反之，则认为是错误
            if main_exists != thumbnail_exists:
                logging.debug(f"Type {image_type_id} 错误检查 - 主图片存在: {main_exists}, 缩略图存在: {thumbnail_exists}")
                return True
                
        elif image_type_id == 3:  # Type 3
            # 检查 deepzoom 目录是否存在但 metadata.xml 缺失
            deepzoom_dir = os.path.join(project_path, image_name, "deepzoom")
            metadata_path = os.path.join(deepzoom_dir, "metadata.xml")
            
            deepzoom_exists = os.path.exists(deepzoom_dir)
            metadata_exists = os.path.exists(metadata_path)
            
            if deepzoom_exists and not metadata_exists:
                logging.debug(f"Type 3 错误检查 - deepzoom 目录存在但 metadata.xml 缺失")
                return True
                
        elif image_type_id == 4:  # Type 4
            # 检查 split 目录是否存在且包含 channel_*.tiff 文件，但检查结果为 False
            split_dir = os.path.join(project_path, image_name, "split")
            
            if os.path.exists(split_dir):
                import glob
                channel_pattern = os.path.join(split_dir, "channel_*.tiff")
                channel_files = glob.glob(channel_pattern)
                
                # 如果 split 目录存在且包含 channel_*.tiff 文件，但 check_type4_status 返回 False
                # 这意味着并非所有通道都完整，应标记为错误
                if channel_files:
                    logging.debug(f"Type 4 错误检查 - split 目录存在且包含 {len(channel_files)} 个通道文件，但检查不完整")
                    return True
        
        return False
        
    except Exception as e:
        logging.error(f"判断是否标记为错误时发生异常: {e}")
        return False


def main():
    """主函数"""
    # 设置日志记录器
    setup_logging()
    logging.info("启动图像状态校准脚本")
    
    # 加载 .env 配置
    logging.info("加载配置文件...")
    config = load_config()
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 打印加载的配置和参数 (用于调试)
    logging.info(f"数据库配置:")
    logging.info(f"  DB_HOST: {config['DB_HOST']}")
    logging.info(f"  DB_PORT: {config['DB_PORT']}")
    logging.info(f"  DB_USER: {config['DB_USER']}")
    logging.info(f"  DB_NAME: {config['DB_NAME']}")
    logging.info(f"  NFS_PROJECT_SAVE_DIR: {config['NFS_PROJECT_SAVE_DIR']}")
    
    logging.info(f"命令行参数:")
    logging.info(f"  Project ID filter: {args.project_id}")
    logging.info(f"  Group ID filter: {args.group_id}")
    
    # 验证参数互斥性
    if args.project_id is not None and args.group_id is not None:
        logging.warning("同时指定了 --project-id 和 --group-id 参数，将优先使用 --group-id")
    
    # 准备数据库配置
    db_config = {
        'host': config['DB_HOST'],
        'port': config['DB_PORT'],
        'user': config['DB_USER'],
        'password': config['DB_PASSWORD'],
        'database': config['DB_NAME']
    }
    
    # 建立数据库连接
    connection, cursor = connect_db(db_config)
    
    if connection is None or cursor is None:
        logging.error("数据库连接失败，程序退出")
        sys.exit(1)
    
    # 初始化计数器
    checked_count = 0
    updated_count = 0
    error_count = 0
    
    try:
        # 查询需要检查的图像记录
        images_to_check = fetch_images_to_check(cursor, args.project_id, args.group_id)
            
        # 获取 NFS 基础路径
        nfs_base_path = config['NFS_PROJECT_SAVE_DIR']
        
        # 遍历查询结果并执行 NFS 状态检查
        for image_record in images_to_check:
            # PostgreSQL返回的字段：image_id, project_id, image_hash, image_type_id, version
            image_id, project_id, image_hash, image_type_id, version = image_record
            checked_count += 1
            
            # 从数据库查询获取当前状态（根据 fetch_images_to_check 的逻辑，应该是 PENDING 或 PROCESSING）
            db_status = STATUS_PENDING if version == 0 else STATUS_PROCESSING  # 简化假设，实际可能需要查询
            
            logging.debug(f"处理图像记录 - ID: {image_id}, 项目ID: {project_id}, Hash: {image_hash}, 类型: {image_type_id}, 版本: {version}")

            # 执行 NFS 文件状态检查
            nfs_completed = check_nfs_status(nfs_base_path, project_id, image_hash, image_type_id)
            
            # 记录检查结果
            status_text = 'COMPLETED' if nfs_completed else 'NOT COMPLETED'
            logging.info(f"Image ID: {image_id} - NFS Status: {status_text}")
            
            # 根据 NFS 检查结果决定是否更新数据库状态
            if nfs_completed:
                # NFS 状态已完成，如果当前数据库状态是 PENDING 或 PROCESSING，则更新为 COMPLETED
                if db_status in [STATUS_PENDING, STATUS_PROCESSING]:
                    update_success = update_image_db_status(cursor, image_id, STATUS_COMPLETED, version)
                    
                    if update_success:
                        # 提交事务
                        connection.commit()
                        updated_count += 1
                        logging.info(f"Image ID: {image_id} - Successfully updated status to COMPLETED in DB.")
                    else:
                        logging.warning(f"Image ID: {image_id} - Failed to update status to COMPLETED in DB (possibly due to version mismatch or record not found).")
                else:
                    logging.debug(f"Image ID: {image_id} - NFS completed but DB status is not PENDING/PROCESSING, no update needed.")
            else:
                # NFS 状态未完成，检查是否应标记为 ERROR
                should_error = should_mark_as_error(nfs_base_path, project_id, image_hash, image_type_id)
                
                if should_error and db_status in [STATUS_PENDING, STATUS_PROCESSING]:
                    # 标记为错误状态
                    update_success = update_image_db_status(cursor, image_id, STATUS_ERROR, version)
                    
                    if update_success:
                        # 提交事务
                        connection.commit()
                        error_count += 1
                        logging.info(f"Image ID: {image_id} - Successfully updated status to ERROR in DB due to incomplete files.")
                    else:
                        logging.warning(f"Image ID: {image_id} - Failed to update status to ERROR in DB (possibly due to version mismatch or record not found).")
                else:
                    # NFS 状态未完成，但不符合错误条件（可能文件完全不存在，表示任务未开始）
                    logging.info(f"Image ID: {image_id} - NFS status is NOT COMPLETED, but no error condition detected. DB status will not be updated.")
        
        logging.info("数据库查询和记录遍历完成")
        
    except Exception as e:
        logging.error(f"发生未预期的错误: {e}")
        # 回滚事务
        if connection:
            connection.rollback()
        sys.exit(1)
    finally:
        # 确保数据库连接被正确关闭
        close_db(connection, cursor)
        
        # 输出总结报告
        logging.info(f"Calibration finished. Checked: {checked_count} images. Updated to COMPLETED: {updated_count} images. Updated to ERROR: {error_count} images.")


if __name__ == "__main__":
    main()