# Image Status Calibrator

一个独立的 Python 工具，用于校准 MedLabel 项目中图片在数据库中的状态与其实际在 NFS 文件系统中的状态。

## 目的

由于竞争状态或其他原因，Python 后端 (`aiLabel_python_backend`) 完成图片处理后，Java 后端 (`NewMedLabelServer`) 可能未能正确更新数据库中的图片状态。此脚本旨在通过直接检查 NFS 文件系统上的文件完整性，来修复这些不一致的状态。

## 功能特性

- **状态校准**: 检查处于 `PENDING` (0) 或 `PROCESSING` (1) 状态的图片。
- **NFS 文件验证**: 根据图片类型 (`imageTypeId`)，严格检查对应的文件是否在 NFS 上完整生成。
- **智能状态更新**:
  - 如果 NFS 文件完整，将数据库状态更新为 `COMPLETED` (2)。
  - 如果 NFS 文件不完整（例如，主文件存在但缩略图缺失），将数据库状态更新为 `ERROR` (3)。
- **乐观锁**: 在更新数据库时使用 `version` 字段进行乐观锁检查，防止并发修改。
- **范围筛选**: 支持通过命令行参数 `--project-id` 或 `--group-id` 指定检查范围，或对整个数据库进行检查。
- **模块化设计**: 代码结构清晰，分为配置加载、数据库处理、NFS 检查和主流程等模块。
- **全面测试**: 包含超过 40 个单元测试和集成测试，确保代码质量和逻辑正确性。

## 项目结构

```
image_status_calibrator/
├── tests/                  # 单元和集成测试
├── .env                    # 配置文件 (需手动创建)
├── .gitignore              # Git 忽略文件
├── config_loader.py        # 配置加载模块
├── constants.py            # 常量定义模块
├── db_handler.py           # 数据库处理模块
├── main_processor.py       # 主流程和入口
├── nfs_checker.py          # NFS 文件检查模块
├── README.md               # 项目说明
└── requirements.txt        # Python 依赖
```

## 使用方法

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 创建并配置 `.env` 文件

在项目根目录 (`image_status_calibrator/`) 下创建一个 `.env` 文件，并填入以下内容：

```env
# 数据库配置
DB_HOST=your_db_host
DB_PORT=your_db_port
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name

# NFS 存储路径配置
NFS_PROJECT_SAVE_DIR=/path/to/your/nfs/projects
```

### 3. 运行校准脚本

- **检查所有项目:**
  ```bash
  python3 main_processor.py
  ```

- **检查特定项目:**
  ```bash
  python3 main_processor.py --project-id 123
  ```

- **检查特定图片组:**
  ```bash
  python3 main_processor.py --group-id 321
  ```

### 4. 运行测试

```bash
python3 -m unittest discover -s tests -v