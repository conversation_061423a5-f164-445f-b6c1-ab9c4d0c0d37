#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NFS 文件检查模块

负责检查 NFS 文件系统中图像文件的完整性和状态。
"""

import os
import logging
import glob


def check_type1_2_status(project_path, image_name):
    """
    检查普通图片（Type 1）和 Dicom 图片（Type 2）的 NFS 状态
    
    Args:
        project_path (str): 项目在 NFS 中的路径
        image_name (str): 图片名称
        
    Returns:
        bool: True 表示文件完整，False 表示文件不完整
    """
    try:
        # 构建主图片路径
        main_image_path = os.path.join(project_path, f"{image_name}.png")
        
        # 构建缩略图路径
        thumbnail_path = os.path.join(project_path, "thumbnail", f"{image_name}.png")
        
        # 检查两个文件是否都存在
        main_exists = os.path.exists(main_image_path)
        thumbnail_exists = os.path.exists(thumbnail_path)
        
        logging.debug(f"Type 1/2 检查 - 主图片: {main_image_path} ({'存在' if main_exists else '不存在'})")
        logging.debug(f"Type 1/2 检查 - 缩略图: {thumbnail_path} ({'存在' if thumbnail_exists else '不存在'})")
        
        return main_exists and thumbnail_exists
        
    except Exception as e:
        logging.error(f"检查 Type 1/2 文件状态时发生错误: {e}")
        return False


def check_type3_status(project_path, image_name):
    """
    检查病理图片（Type 3）的 NFS 状态
    
    Args:
        project_path (str): 项目在 NFS 中的路径
        image_name (str): 图片名称
        
    Returns:
        bool: True 表示文件完整，False 表示文件不完整
    """
    try:
        # 构建 deepzoom metadata.xml 路径
        metadata_path = os.path.join(project_path, image_name, "deepzoom", "metadata.xml")
        
        # 检查 metadata.xml 文件是否存在
        metadata_exists = os.path.exists(metadata_path)
        
        logging.debug(f"Type 3 检查 - metadata.xml: {metadata_path} ({'存在' if metadata_exists else '不存在'})")
        
        return metadata_exists
        
    except Exception as e:
        logging.error(f"检查 Type 3 文件状态时发生错误: {e}")
        return False


def check_type4_status(project_path, image_name):
    """
    检查多通道图片（Type 4）的 NFS 状态
    
    严格按照计划逻辑实现：
    1. 获取 split 目录下所有 channel_*.tiff 文件列表
    2. 如果列表为空（即没有找到任何 channel_*.tiff 文件），则返回 False
    3. 遍历所有找到的 channel_*.tiff 文件，解析通道号
    4. 检查每个通道对应的 deepzoom/metadata.xml 是否存在
    5. 只有当所有找到的 channel_*.tiff 文件都有对应的有效 metadata.xml 时，才返回 True
    
    Args:
        project_path (str): 项目在 NFS 中的路径
        image_name (str): 图片名称
        
    Returns:
        bool: True 表示所有通道文件完整，False 表示文件不完整
    """
    try:
        # 构建 split 目录路径
        split_dir = os.path.join(project_path, image_name, "split")
        
        # 查找 channel_*.tiff 文件
        channel_pattern = os.path.join(split_dir, "channel_*.tiff")
        channel_files = glob.glob(channel_pattern)
        
        # 如果没有找到任何 channel_*.tiff 文件，返回 False
        if not channel_files:
            logging.debug(f"Type 4 检查 - 未找到 channel_*.tiff 文件: {split_dir}")
            return False
        
        logging.debug(f"Type 4 检查 - 找到 {len(channel_files)} 个通道文件")
        
        # 严格检查：遍历所有找到的 channel_*.tiff 文件
        for channel_file in channel_files:
            # 从文件名提取通道号，例如：channel_1.tiff -> 1
            channel_filename = os.path.basename(channel_file)
            try:
                # 提取通道号：channel_X.tiff -> X
                channel_num = channel_filename.replace("channel_", "").replace(".tiff", "")
                
                # 构建对应通道的 deepzoom metadata 路径
                channel_metadata_path = os.path.join(project_path, image_name, channel_num, "deepzoom", "metadata.xml")
                
                # 检查对应的 metadata.xml 是否存在
                if not os.path.exists(channel_metadata_path):
                    logging.debug(f"Type 4 检查 - 通道 {channel_num} 的 metadata.xml 不存在: {channel_metadata_path}")
                    return False  # 任何一个找到的 channel_*.tiff 文件没有对应的有效 metadata.xml，则返回 False
                else:
                    logging.debug(f"Type 4 检查 - 通道 {channel_num} 的 metadata.xml 存在: {channel_metadata_path}")
                    
            except Exception as e:
                logging.warning(f"解析通道文件名时发生错误: {channel_file}, 错误: {e}")
                return False  # 解析失败也认为是不完整
        
        # 所有找到的 channel_*.tiff 文件都有对应的有效 metadata.xml
        logging.debug("Type 4 检查 - 所有通道都有对应的 metadata.xml，状态完整")
        return True
        
    except Exception as e:
        logging.error(f"检查 Type 4 文件状态时发生错误: {e}")
        return False


def check_nfs_status(nfs_base_path, project_id, image_name, image_type_id):
    """
    根据图片类型检查 NFS 文件状态
    
    Args:
        nfs_base_path (str): NFS 基础路径
        project_id (int): 项目 ID
        image_name (str): 图片名称
        image_type_id (int): 图片类型 ID
        
    Returns:
        bool: True 表示 NFS 文件状态为"完成"，False 表示"未完成"
    """
    try:
        # 构建项目路径
        project_path = os.path.join(nfs_base_path, str(project_id))
        
        logging.debug(f"检查 NFS 状态 - 项目路径: {project_path}")
        logging.debug(f"检查 NFS 状态 - 图片名称: {image_name}")
        logging.debug(f"检查 NFS 状态 - 图片类型: {image_type_id}")
        
        # 根据图片类型调用对应的检查函数
        if image_type_id in [1, 2]:  # Type 1 (普通) / Type 2 (Dicom)
            return check_type1_2_status(project_path, image_name)
        elif image_type_id == 3:  # Type 3 (病理)
            return check_type3_status(project_path, image_name)
        elif image_type_id == 4:  # Type 4 (多通道)
            return check_type4_status(project_path, image_name)
        else:
            logging.warning(f"未知的图片类型 ID: {image_type_id}")
            return False
            
    except Exception as e:
        logging.error(f"检查 NFS 状态时发生错误: {e}")
        return False