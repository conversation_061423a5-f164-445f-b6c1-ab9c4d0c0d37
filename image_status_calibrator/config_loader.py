#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载模块

负责从 .env 文件加载和验证配置项。
"""

import os
import sys
import logging
from dotenv import load_dotenv, find_dotenv


def load_config():
    """
    加载 .env 配置文件并验证必要的配置项
    
    Returns:
        dict: 包含所有验证过的配置项的字典
        
    Raises:
        SystemExit: 当配置项缺失或无效时退出程序
    """
    # 加载 .env 文件
    load_dotenv(find_dotenv())
    
    # 必要的配置项列表
    required_configs = [
        'DB_HOST',
        'DB_PORT', 
        'DB_USER',
        'DB_PASSWORD',
        'DB_NAME',
        'NFS_PROJECT_SAVE_DIR'
    ]
    
    # 从环境变量中读取配置
    config = {}
    missing_configs = []
    
    for config_key in required_configs:
        value = os.getenv(config_key)
        if value is None or value.strip() == '':
            missing_configs.append(config_key)
        else:
            config[config_key] = value
    
    # 检查是否有缺失的配置项
    if missing_configs:
        logging.error(f"缺失必要的配置项: {', '.join(missing_configs)}")
        logging.error("请检查 .env 文件中是否包含所有必要的配置项")
        sys.exit(1)
    
    # 验证数据库端口为数字
    try:
        config['DB_PORT'] = int(config['DB_PORT'])
    except ValueError:
        logging.error(f"DB_PORT 配置项必须为数字，当前值: {config['DB_PORT']}")
        sys.exit(1)
    
    return config