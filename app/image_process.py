from flask import Flask, request, jsonify
from config.config import (RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD,
                    RABBITMQ_CONNECTION_TIMEOUT, RABBITMQ_HEARTBEAT, RABBITMQ_SOCKET_TIMEOUT,
                    RABBITMQ_BLOCKED_CONNECTION_TIMEOUT, RABBITMQ_CONNECTION_ATTEMPTS, RABBITMQ_RETRY_DELAY)

# -- 普通图片复制所需库--
import shutil

# -- dicom图片转化所需库 --
import SimpleITK as sitk
import numpy as np
import cv2

# -- 病理图转化所需库 --
import pika.exceptions
import requests
import time
import openslide
from openslide import OpenSlide
import openslide.deepzoom
import threading
from tempfile import NamedTemporaryFile
from PIL import Image
from app.kfbreader import KFBSlide
Image.MAX_IMAGE_PIXELS = None

# -- 多通道图转化所需库 --
import math
import tifffile as tiff
from functools import partial

# -- 消息队列所需库 --
import pika
import json
import os
import os.path as osp

# -- Celery所需库 --
from . import celery
from celery import Celery
from billiard import Pool  # 替换 multiprocessing.Pool
import multiprocessing
from app import connect_redis
from config.config import PROJECT_SAVE_DIR
from functools import wraps
import contextlib

# 声明队列
image_task_finish_callback_queue = 'medlabel_image_convert_task_finish_callback_queue'
redis_client = connect_redis()
global_img = None

# ================================= 连接管理辅助函数 ==================================

@contextlib.contextmanager
def get_rabbitmq_connection():
    """RabbitMQ连接上下文管理器，确保连接正确关闭"""
    connection = None
    channel = None
    try:
        user_info = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        # 添加连接参数以提高稳定性
        connection_params = pika.ConnectionParameters(
            host=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            credentials=user_info,
            heartbeat=RABBITMQ_HEARTBEAT,
            blocked_connection_timeout=RABBITMQ_BLOCKED_CONNECTION_TIMEOUT,
            connection_attempts=RABBITMQ_CONNECTION_ATTEMPTS,
            retry_delay=RABBITMQ_RETRY_DELAY,
            socket_timeout=RABBITMQ_SOCKET_TIMEOUT,
        )
        connection = pika.BlockingConnection(connection_params)
        channel = connection.channel()

        # 声明队列 - 注释掉队列声明功能
        # channel.queue_declare(
        #     queue=image_task_finish_callback_queue,
        #     durable=True,
        #     arguments={
        #         'x-dead-letter-exchange': 'dlx.direct',
        #         'x-dead-letter-routing-key': 'image_convert_task_finish_callback.dlq'
        #     }
        # )

        yield channel

    except Exception as e:
        print(f"RabbitMQ连接错误: {e}")
        raise
    finally:
        try:
            if channel and not channel.is_closed:
                channel.close()
        except Exception as e:
            print(f"关闭channel时出错: {e}")
        try:
            if connection and not connection.is_closed:
                connection.close()
        except Exception as e:
            print(f"关闭connection时出错: {e}")

def send_progress_update(taskId, imageId, status, progress, result):
    """发送进度更新消息，使用连接管理器"""
    try:
        # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
        # with get_rabbitmq_connection() as channel:
        #     message = {
        #         "taskId": taskId,
        #         "imageId": imageId,
        #         "status": status,
        #         "progress": progress,
        #         "result": result
        #     }
        #     channel.basic_publish(
        #         exchange='',
        #         routing_key=image_task_finish_callback_queue,
        #         body=json.dumps(message),
        #         properties=pika.BasicProperties(
        #             delivery_mode=2,  # 消息持久化
        #         )
        #     )
        print(f"进度更新已发送: {taskId} - {progress} (队列消息发送已禁用)")
    except Exception as e:
        print(f"发送进度更新失败: {e}")
        # 不抛出异常，避免影响主任务

# ================================= 普通图像辅助函数 ==================================

def thumbnail_convert(input_dir, output_dir):
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 遍历输入文件夹中的所有文件
    for filename in os.listdir(input_dir):
        if filename.lower().endswith('.png'):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)

            try:
                # 打开图像并转换尺寸
                with Image.open(input_path) as img:
                    img = img.resize((224, 224), Image.Resampling.LANCZOS)
                    img.save(output_path)
                print(f"已处理：{filename}")
            except Exception as e:
                print(f"处理 {filename} 时出错：{e}")

def thumbnail_convert_for_single_image(input_path, output_path, imageName):
    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)

    try:
        # 打开图像并转换尺寸
        with Image.open(input_path) as img:
            img = img.resize((224, 224), Image.Resampling.LANCZOS)
            img.save(os.path.join(output_path, f"{imageName}.png"))
        print(f"已处理：{input_path}")
    except Exception as e:
        print(f"处理 {input_path} 时出错：{e}")

# =================================== 病理辅助函数 ===================================

def set_permissions(path):
    for root, dirs, files in os.walk(path):
        if "deepzoom" not in root:
            continue
        os.chmod(root, 0o777)
        for d in dirs:
            os.chmod(os.path.join(root, d), 0o777)
        for f in files:
            os.chmod(os.path.join(root, f), 0o777)

def get_slide(wsi_path):
    '''获取切片对象'''
    ext = osp.splitext(wsi_path)[1].lower()
    if ext in ['.svs', '.tif', '.tiff', '.mrxs']:
        slide = OpenSlide(wsi_path)
    elif ext in ['.kfb']:
        slide = KFBSlide(wsi_path)
    else:
        raise ValueError(f'Unsupport extension: {wsi_path}')
    return slide

def read_region(slide, location, level, size, zero_level_loc=True) -> Image:
    '''
    读取切片指定层级的指定区域
    slide: get_slide函数获取的切片对象
    location: 要读取区域的左上角坐标(x, y)
    level: 要读取的缩放层级
    size: 要读取的区域图片大小
    zero_level_loc: 若为True，则location参数为左上角在level 0上的坐标，否则location为当前level上的左上角坐标
    '''
    ratio = slide.level_downsamples[level] / slide.level_downsamples[0]
    if isinstance(slide, KFBSlide):
        if zero_level_loc:
            return Image.fromarray(slide.read_region((round(location[0]/ratio), round(location[1]/ratio)), level, size))
        return Image.fromarray(slide.read_region(location, level, size))
    elif isinstance(slide, OpenSlide):
        if zero_level_loc:
            return slide.read_region(location, level, size)
        return slide.read_region((round(location[0]*ratio), round(location[1]*ratio)), level, size)
    else:
        raise ValueError(f'Unsupport slide: {type(slide)}')

def get_tile(slide, level, x, y, size=None):
        """
        获取指定层级的指定瓦片
        """
        # 计算区域的左上角坐标和区域大小
        level_size = slide.level_dimensions[level]
        tile_size = 1024
        location = (x * tile_size, y * tile_size)
        return read_region(slide, location, level, (tile_size, tile_size))

def time_logger(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"函数 {func.__name__} 执行时间: {elapsed_time:.2f} 秒")
        return result
    return wrapper


def process_tile(level, x, y, slide_path, level_dir, tileSize, overlap, taskId, totalNum, imageId):
    """处理单个切片并更新 Redis 进度"""
    try:
        slide = get_slide(slide_path)
        dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)
        tile = dz_gen.get_tile(level, (x, y))

        tile_path = os.path.join(level_dir, f'{x}_{y}.jpeg')
        tile.save(tile_path, 'JPEG')

        redis_client.incr(f'image_convert_task_processed:{taskId}')
        processed_tiles = int(redis_client.get(f'image_convert_task_processed:{taskId}'))
        progress = round(processed_tiles / totalNum, 2)

        # 每处理500个瓦片发送一次进度更新
        if processed_tiles % 500 == 0:
            send_progress_update(
                taskId=taskId,
                imageId=imageId,
                status=1,
                progress=progress,
                result=f"{taskId}: 图像转化任务更新"
            )

    except Exception as e:
        print(f"Error processing tile {x}, {y}: {e}")

@celery.task
@time_logger
def generate_deep_zoom(mrxs_path, outputDir, tileSize, overlap, taskId, imageId):
    """Celery 任务，使用 billiard.Pool 进行多进程并行"""
    try:
        slide = get_slide(mrxs_path)
        os.makedirs(outputDir, exist_ok=True)
    except openslide.OpenSlideError as e:
        print(f"Cannot open slide at {mrxs_path}, {e}")
        return
        # return jsonify({'code': 1, 'msg': f'打开文件 {mrxs_path} 失败，请检查文件是否正确'})

    dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)
    totalNum = sum(x * y for x, y in dz_gen.level_tiles)

    # 获取图像尺寸
    width, height = slide.dimensions

    print(f"图像尺寸: {width} x {height}")
    print(f"计算切片总数为: {totalNum}")

    num_processes = max(1, 64)

    print(f"当前可使用进程数为: {str(num_processes)}")

    # 初始化 Redis 进度记录
    redis_client.set(f'image_convert_task_progress:{taskId}', '0')

    # 发送任务开始通知
    send_progress_update(
        taskId=taskId,
        imageId=imageId,
        status=1,
        progress=0.0,
        result=f"{taskId}: 开始生成深度图"
    )

    try:
        tasks = []

                # 生成 metadata.xml 文件
        with open(os.path.join(outputDir, f'metadata.xml'), 'w') as f:
            f.write(dz_gen.get_dzi('JPEG'))
        os.chmod(os.path.join(outputDir, f'metadata.xml'), 0o777)

        # 处理每个层级的切片
        for level in range(dz_gen.level_count):
            level_dir = os.path.join(outputDir, "imgs", str(level))
            os.makedirs(level_dir, exist_ok=True)
            tiles = dz_gen.level_tiles[level]

            for x in range(tiles[0]):
                for y in range(tiles[1]):
                    tasks.append((level, x, y, mrxs_path, level_dir, tileSize, overlap, taskId, totalNum, imageId))

            set_permissions(level_dir)

        with Pool(processes=num_processes) as pool:
            for args in tasks:
                pool.apply_async(process_tile, args=args)

            pool.close()
            pool.join()

        # 生成元数据
        generate_metadata(width, height, outputDir, tileSize)

        # 发送任务完成通知
        send_progress_update(
            taskId=taskId,
            imageId=imageId,
            status=2,
            progress=1.0,
            result=f"{taskId}: 图像转化任务完成"
        )

    except Exception as e:
        # 出现异常时发送错误回调
        print(f"在生成深度图时发生异常: {e}")
        send_progress_update(
            taskId=taskId,
            imageId=imageId,
            status=3,
            progress=0.0,
            result=f"图像转化任务失败: {str(e)}"
        )

# ================================= Dicom辅助函数 ==================================

def normalization(x):
    window_width, window_level = 1800, 1000
    low = window_level - window_width / 2
    high = window_level + window_width / 2

    if x < low:
        return 0
    elif x > high:
        return 255
    else:
        return (int)((x - low) * (255 / window_width))

def dicom_convert(imageUrl, outputDir, imageName):
    ds_array = sitk.ReadImage(imageUrl)
    pixel_array = sitk.GetArrayFromImage(ds_array)
    normalized_array = []
    for z in pixel_array:
        y_array = []
        for y in z:
            x_array = []
            for x in y:
                x = normalization(x)
                x_array.append(x)
            y_array.append(x_array)
        normalized_array.append(y_array)
    img = sitk.GetImageFromArray(np.array(normalized_array).astype('uint8'))
    sitk.WriteImage(img, os.path.join(outputDir, f"{imageName}.png"))
    os.chmod(os.path.join(outputDir, f"{imageName}.png"), 0o777)

# =================================== 多通道图像辅助函数 ===================================

def init_worker(shared_img):
    global global_img
    global_img = shared_img

def process_channel(i, outputDir):
    # 全局变量中取出通道
    channel = global_img[i]
    output_path = os.path.join(outputDir, f'channel_{i+1}.tiff')
    tiff.imwrite(output_path, channel)
    return f"[PID {os.getpid()}] 写入通道 {i+1}"

@celery.task
@time_logger
def channel_convert(imageUrl, outputDir, projectId, imageName):
    img = tiff.imread(imageUrl)
    print("读取qptiff图像完成")
    channels = img.shape[0]
    tasks = [(i, outputDir) for i in range(channels)]

    with Pool(processes=channels, initializer=init_worker, initargs=(img,)) as pool:
        results = []
        for args in tasks:
            r = pool.apply_async(process_channel, args=args)
            results.append(r)
        pool.close()
        pool.join()
        for r in results:
            print(r.get())

    tiffPaths, outputDirs = [], []
    for i in range(channels):
        tiffPaths.append(os.path.join(outputDir, f'channel_{i+1}.tiff'))
        outputDirs.append(os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, str(i+1)))
    return tiffPaths, outputDirs

def count_total_tile(max_level, width, height, tileSize):
    total_tiles = 0
    # 预先计算每个层级的切片数
    level_tile_counts = {}
    for level in range(max_level + 1):
        # 计算当前层的缩放比例
        scale_factor = 2 ** (max_level - level)
        scaled_height = math.ceil(height / scale_factor)
        scaled_width = math.ceil(width / scale_factor)

        # 计算该层的 Tile 数量
        num_tiles_x = math.ceil(scaled_width / tileSize)
        num_tiles_y = math.ceil(scaled_height / tileSize)

        # 存储每个层级的 Tile 数量
        level_tile_counts[level] = (num_tiles_x, num_tiles_y)

        # 累加该层的总切片数
        total_tiles += num_tiles_x * num_tiles_y
    # 打印总层数和总切片数
    print(f"Total levels: {max_level + 1}")
    print(f"Total tiles: {total_tiles}")
    return level_tile_counts

def generate_metadata(width, height, outputDir, tileSize):
    metadata = f"""<Image TileSize="{tileSize}" Overlap="0" Format="JPEG" xmlns="http://schemas.microsoft.com/deepzoom/2008">
  <Size Width="{str(width)}" Height="{str(height)}" /></Image>"""
    with open(os.path.join(outputDir, "metadata.xml"), "w") as f:
        f.write(metadata)

@celery.task
@time_logger
def generate_deep_zoom_for_TIFF(mrxs_path, outputDir, tileSize, overlap, taskId, imageId):
    """Celery 任务，使用 billiard.Pool 进行多进程并行"""
    # 读取图像
    image = tiff.imread(mrxs_path)

    if image.ndim != 2:
        raise ValueError(f"图像不是灰度图（2D），而是 {image.shape}")

    # 若不是 uint8，做简单归一化
    if image.dtype != np.uint8:
        image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)

    height, width = image.shape[:2]
    max_level = int(math.ceil(math.log(max(height, width), 2)))

    level_tile_counts = count_total_tile(max_level, width, height, tileSize)

    # 初始化 Redis 进度记录
    # redis_client.set(f'image_convert_task_progress:{taskId}', '0')
    # 注释掉队列连接和声明功能
    # user_info = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
    # connection = pika.BlockingConnection(pika.ConnectionParameters(RABBITMQ_HOST, RABBITMQ_PORT, '/', user_info))
    # reply_channel = connection.channel()
    # reply_channel.queue_declare(queue=image_task_finish_callback_queue,
    #                             durable=True,
    #                             arguments={
    #                                 'x-dead-letter-exchange': 'dlx.direct',
    #                                 'x-dead-letter-routing-key': 'image_convert_task_finish_callback.dlq'
    #                             })

    try:
        # 处理每个层级的切片
        for level in range(max_level + 1):
            scale_factor = 2 ** (max_level - level)
            scaled_width = math.ceil(width / scale_factor)
            scaled_height = math.ceil(height / scale_factor)

            # 使用 PIL 对整个图像进行缩放
            pil_image = Image.fromarray(image)
            level_image = pil_image.resize((scaled_width, scaled_height), Image.Resampling.LANCZOS)

            level_dir = os.path.join(outputDir, "imgs", str(level))
            os.makedirs(level_dir, exist_ok=True)

            num_tiles_x, num_tiles_y = level_tile_counts[level]

            for x in range(num_tiles_x):
                for y in range(num_tiles_y):
                    left = x * tileSize
                    top = y * tileSize
                    right = min(left + tileSize, scaled_width)
                    bottom = min(top + tileSize, scaled_height)

                    if right <= left or bottom <= top:
                        continue

                    # 从缩放后的图像裁剪出 tile
                    tile = level_image.crop((left, top, right, bottom))

                    tile_filename = os.path.join(level_dir, f"{x}_{y}.jpeg")
                    tile.save(tile_filename, "JPEG")

            set_permissions(level_dir)

        generate_metadata(width, height, outputDir, tileSize)
        os.chmod(os.path.join(outputDir, f'metadata.xml'), 0o777)

    except Exception as e:
        # 出现异常时发送错误回调
        print(f"在生成TIFF深度图时发生异常: {e}")

# ================================== 图像类型调用函数 ==================================

def normal_image_process(data, reply_channel):
    try:
        taskId = data['taskId']
        projectId = data['projectId']
        imageName = data['imageName']
        imageId = data['imageId']
        imageUrl = data['imageUrl']
        outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId))

        if not all([taskId, projectId, imageUrl, imageName, outputDir]):
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "缺少参数"}))
            print(f"缺少参数: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
            return

        os.makedirs(outputDir, exist_ok=True)
        shutil.copy(imageUrl, os.path.join(outputDir, f"{imageName}.png"))
        thumbnail_convert_for_single_image(os.path.join(outputDir, f"{imageName}.png"), os.path.join(outputDir, "thumbnail"), imageName)
        os.chmod(os.path.join(outputDir, f"{imageName}.png"), 0o777)
        os.chmod(os.path.join(outputDir, "thumbnail", f"{imageName}.png"), 0o777)
        # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
        # reply_channel.basic_publish(exchange='',
        #                             routing_key=image_task_finish_callback_queue,
        #                             body=json.dumps({"taskId": taskId,
        #                                             "imageId": imageId,
        #                                             "status": 2,
        #                                             "progress": 1.0,
        #                                             "result": taskId + ": 图像转化任务完成"}))
        print(f"图像转化任务完成: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
    except Exception as e:
        print(f"Exception in thread : {e}")

def dicom_image_process(data, reply_channel):
    try:
        taskId = data['taskId']
        projectId = data['projectId']
        imageName = data['imageName']
        imageId = data['imageId']
        imageUrl = data['imageUrl']
        outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId))

        if not all([taskId, projectId, imageUrl, imageName, outputDir]):
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "缺少参数"}))
            print(f"缺少参数: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
            return

        os.makedirs(outputDir, exist_ok=True)
        dicom_convert(imageUrl, outputDir, imageName)
        thumbnail_convert_for_single_image(os.path.join(outputDir, f"{imageName}.png"), os.path.join(outputDir, "thumbnail"), imageName)
        os.chmod(os.path.join(outputDir, f"{imageName}.png"), 0o777)
        os.chmod(os.path.join(outputDir, "thumbnail", f"{imageName}.png"), 0o777)
        # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
        # reply_channel.basic_publish(exchange='',
        #                             routing_key=image_task_finish_callback_queue,
        #                             body=json.dumps({"taskId": taskId,
        #                                             "imageId": imageId,
        #                                             "status": 2,
        #                                             "progress": 1.0,
        #                                             "result": taskId + ": 图像转化任务完成"}))
        print(f"DICOM图像转化任务完成: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
    except Exception as e:
        print(f"Exception in thread : {e}")


def patho_image_process(data, reply_channel):
    taskId = data['taskId']
    projectId = data['projectId']
    imageName = data['imageName']
    imageId = data['imageId']
    mrxs_path = data['imageUrl']
    outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "deepzoom")
    tileSize = 1024
    overlap = 0

    try:
        if not all([taskId, projectId, mrxs_path, imageName, outputDir, tileSize]) or overlap is None:
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "缺少参数"}))
            print(f"病理图处理缺少参数: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
            return

        # if project_status.get(projectId) and project_status[projectId]["status"] == "processing":
        #     return jsonify({"code": 1, "message": "项目正在处理中，请勿重复提交"})

        # response = requests.get(mrxsPath)
        if(not os.path.exists(mrxs_path)):
            print(f"病理图文件不存在: {mrxs_path}")
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "病理图文件不存在: {" + mrxs_path + "}"}))
            print(f"病理图文件不存在: taskId={taskId}, imageId={imageId}, path={mrxs_path} (队列消息发送已禁用)")
            return  # 添加缺失的return语句

        print(f"开始处理病理图文件: {mrxs_path}")
        # 将任务发送到 Celery 后台执行
        task = generate_deep_zoom.apply_async(args=[mrxs_path, outputDir, tileSize, overlap, taskId, imageId])
        print(f"Celery任务已提交: {task.id}")

    except Exception as e:
        print(f"patho_image_process异常: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        # 发送错误消息 - 注释掉队列消息发送功能
        try:
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": f"处理异常: {str(e)}"}))
            print(f"patho_image_process异常: taskId={taskId}, imageId={imageId}, error={str(e)} (队列消息发送已禁用)")
        except Exception as publish_error:
            print(f"发送错误消息失败: {publish_error}")

def channel_image_process(data, reply_channel):
    taskId = data['taskId']
    projectId = data['projectId']
    imageName = data['imageName']
    imageId = data['imageId']
    mrxs_path = data['imageUrl']
    outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "split")
    tileSize = 1024
    overlap = 0

    try:
        if not all([taskId, projectId, mrxs_path, imageName, outputDir, tileSize]) or overlap is None:
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "缺少参数"}))
            print(f"通道图处理缺少参数: taskId={taskId}, imageId={imageId} (队列消息发送已禁用)")
            return

        # if project_status.get(projectId) and project_status[projectId]["status"] == "processing":
        #     return jsonify({"code": 1, "message": "项目正在处理中，请勿重复提交"})

        # response = requests.get(mrxsPath)
        if(not os.path.exists(mrxs_path)):
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "病理图文件不存在: {" + mrxs_path + "}"}))
            print(f"通道图文件不存在: taskId={taskId}, imageId={imageId}, path={mrxs_path} (队列消息发送已禁用)")
            return

        os.makedirs(outputDir, exist_ok=True)
        tiffPaths, outputDirs = channel_convert(mrxs_path, outputDir, projectId, imageName)
        print("通道转换完成")

        for i in range(len(tiffPaths)):
            os.makedirs(outputDirs[i], exist_ok=True)
            task = generate_deep_zoom_for_TIFF.apply_async(args=[tiffPaths[i], outputDirs[i], tileSize, overlap, taskId, imageId])

    except Exception as e:
        print(f"Exception in thread : {e}")


def image_process(channel, method, properties, body):
    """ 创建病理图切片

    Args:
        channel, method, properties, body
        body (string) : 队列中的消息主体

    Returns:
        message { code: int, msg: string }: 处理结果
    """
    try:
        print("接受到消息，开始执行图像转化任务")
        # 连接到RabbitMQ服务器
        data = json.loads(body.decode('utf-8'))
        print(data)
        taskId = data['taskId']
        imageTypeId = data['imageTypeId']
        imageId = data['imageId']
        # 注释掉队列连接和声明功能
        # user_info = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        # connection = pika.BlockingConnection(pika.ConnectionParameters(RABBITMQ_HOST, RABBITMQ_PORT, '/', user_info))
        # reply_channel = connection.channel()
        # reply_channel.queue_declare(queue=image_task_finish_callback_queue,
        #                             durable=True,
        #                             arguments={
        #                                 'x-dead-letter-exchange': 'dlx.direct',
        #                                 'x-dead-letter-routing-key': 'image_convert_task_finish_callback.dlq'
        #                             })
        reply_channel = None  # 设置为None，避免后续代码报错

        if imageTypeId == 1:
            normal_image_process(data, reply_channel)
        elif imageTypeId == 2:
            dicom_image_process(data, reply_channel)
        elif imageTypeId == 3:
            patho_image_process(data, reply_channel)
        elif imageTypeId == 4:
            channel_image_process(data, reply_channel)
        else:
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # reply_channel.basic_publish(exchange='',
            #                 routing_key=image_task_finish_callback_queue,
            #                 body=json.dumps({"taskId": taskId,
            #                                 "imageId": imageId,
            #                                 "status": 3,
            #                                 "progress": 0.0,
            #                                 "result": "imageTypeId无法识别"}))
            print(f"imageTypeId无法识别: taskId={taskId}, imageId={imageId}, imageTypeId={imageTypeId} (队列消息发送已禁用)")
    except Exception as e:
        print(f"image_process主函数异常: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        # 尝试发送错误消息（如果可能的话） - 注释掉队列消息发送功能
        try:
            if 'data' in locals() and 'reply_channel' in locals():
                taskId = data.get('taskId', 'unknown')
                imageId = data.get('imageId', 'unknown')
                # reply_channel.basic_publish(exchange='',
                #                 routing_key=image_task_finish_callback_queue,
                #                 body=json.dumps({"taskId": taskId,
                #                                 "imageId": imageId,
                #                                 "status": 3,
                #                                 "progress": 0.0,
                #                                 "result": f"主函数异常: {str(e)}"}))
                print(f"主函数异常: taskId={taskId}, imageId={imageId}, error={str(e)} (队列消息发送已禁用)")
        except Exception as publish_error:
            print(f"发送错误消息失败: {publish_error}")