import json
from flask import Blueprint
from flask.globals import request
from asm.datas.models import DModel, DTask, RProjectTask, d_Projects, DOrgUser,DProjectUser, d_Hits
import xml.etree.ElementTree as ET
from time import time
from datetime import datetime
from multiprocessing import Process
from collections import defaultdict as ddict
import asyncio
import traceback
from sqlalchemy import text

from rq import Queue

from app import connect_redis
redisClient = connect_redis()
redis_queue = Queue(connection=redisClient)

import openslide
import openslide.deepzoom
import os
import threading

# from config import app
from app import db, logger, DbSession
from flask import request, jsonify

bp = Blueprint('data', __name__)
project_status = {}

import uuid

def generate_random_identifier():

    random_uuid = uuid.uuid4()
    return str(random_uuid)


def xml_to_dict(element):
    result = {}
    attributes = element.attrib
    result.update(attributes)

    result["Size"] = {}
    result['Size'].update(element[0].attrib)
    result['Url'] = element.tag.split("}")[0].split("{")[1]

    return result


@bp.route('/getImgMetadata', methods=['POST'])
def getImgMetadata():
    data = request.json
    projectId = data.get('projectId')
    
    project = d_Projects.query_project(projectId)
    slide_name = os.path.splitext(os.path.basename(project.mrxsAddress))[0]

    xml_path = "/nfs/dataturk/uploads/{}/{}/image.xml".format(projectId, slide_name)


    if os.path.exists(xml_path):
        tree = ET.parse(xml_path)
        root = tree.getroot()
        root_dict = xml_to_dict(root)
        return jsonify(root_dict)
    else:
        return jsonify({})


@bp.route('/getDeepZoomStatus', methods=['POST'])
def deepZoomStatus():
    data = request.json
    projectId = data.get('projectId')
    
    lock = threading.Lock()
    
    lock.acquire()
    try:
        if projectId in project_status:
            status = project_status[projectId]
        else:
            status = None
    finally:
        lock.release()
        

    if status is not None:   
        return jsonify({
            "code": 0,
            "msg": "success get deep zoom status",
            **status
        })
    else:
        return jsonify({'code': 1, 'msg': f'该项目不存在处理中的DeepZoom任务'})


@bp.route('/createDeepZoomForPatho', methods=['POST'])
def createDeepZoom():
    data = request.json
    projectId = data.get('projectId')
    mrxsPath = data.get('mrxsPath')
    outputDir = data.get('outputDir')
    tileSize = data.get('tileSize')
    overlap = data.get('overlap')
    
    slide_name = os.path.splitext(os.path.basename(mrxsPath))[0]
    
    if not all([projectId, mrxsPath, outputDir, tileSize]) or overlap is None:
        return jsonify({"code": 1, "message": "缺少参数"})
    
    if project_status.get(projectId) and project_status[projectId]["status"] == "processing":
        return jsonify({"code": 1, "message": "项目正在处理中，请勿重复提交"})
        
    # 检查mrxsPath路径是否存在
    if(not os.path.exists(mrxsPath)):
        return jsonify({'code': 1, 'msg': f'病理图文件不存在: {mrxsPath}'})
    
    try:
        from PIL import Image
        Image.MAX_IMAGE_PIXELS = None
        slide = openslide.open_slide(mrxsPath)
    except openslide.OpenSlideError:
        logger.error(f"Cannot open slide at {mrxsPath}")
        return jsonify({'code': 1, 'msg': f'打开文件 {mrxsPath} 失败，请检查文件是否正确'})
    
    
    dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)

    totalNum = sum(x*y for x, y in dz_gen.level_tiles)
    
    lock = threading.Lock()
    lock.acquire()
    try:
        project_status[projectId] = {"currentNum": 0, "totalNum": totalNum, "level": 0, "status": "processing"}
    finally:
        lock.release()
    
    try:
        thread = threading.Thread(target=generate_deep_zoom, args=(projectId, dz_gen, outputDir, slide_name, lock))
        thread.start()
        return jsonify({"message": f"项目 {projectId}: deep zoom格式图像创建成功启动"}), 200
    
    except Exception as e:
        logger.error("CreateDeepZoom Error: ", e)
        lock.acquire()
        try:
            project_status[projectId]["status"] = "failed"
        finally:
            lock.release()
        return jsonify({"code": 1, "message": f"项目 {projectId}: deep zoom格式图像创建失败"})
    


def process_mrxs(
    userId=None,
    rules=None,
    imageType=None, 
    taskType=None,
    orgId=None,
    projectId=None, 
    projectName=None,  
    taskId=None, 
    mrxsPath=None, 
    tileSize=None, 
    overlap=None, 
    outputDir=None, 
    resume=False
    ):

    this_session = DbSession() # 需要在这里实例化一个session
    new_relation = RProjectTask.find_relation_by_project_id(this_session, projectId)
    
    if not resume:

        current = datetime.now()

        project = d_Projects(
            id=projectId,
            name= projectName,
            taskRules=rules,
            mrxsAddress=mrxsPath,
            created_timestamp=current,
            updated_timestamp=current,
            imageType=imageType,
            taskType=taskType,
            orgId=orgId,
            isPublic=False,
            labelsDone=0,
            minGoldenHITs=0,
            totalStorageInMBs=0,
            validateWithGoldenHITs=0,
            status='NONE',
            totalHits=1,
            totalDoneHits=0
        )

        new_project_user = DProjectUser(
            created_timestamp=current,
            userId=userId,
            updated_timestamp=current,
            role='OWNER',
            projectId=projectId,
        )

        slide_name = os.path.splitext(os.path.basename(mrxsPath))[0]
        new_output_dir = os.path.join(outputDir, slide_name)
        d_hits_dir = '/uploads/{}/{}'.format(projectId, slide_name)

        new_d_hit = d_Hits(
            compressFactor=1,
            created_timestamp=current,
            data=d_hits_dir,
            goldenHITResultId=0,
            isGoldenHIT = False,
            isURL = False,
            projectId = projectId,
            status = 'notDone',
            evaluation="NONE",
            updated_timestamp = current
        )
        
        this_session.add(new_d_hit)
        this_session.add(project)
        this_session.add(new_project_user)
        this_session.commit()

    else:
        new_relation.status = "pending"
        new_relation.message = ""
        new_relation.successImages = 0
        new_relation.failedImages = 0
        new_relation.totalImages = 0

        this_session.commit()

    def fail_callback(new_relation, msg):

        # return
        new_relation.status = "fail"
        new_relation.message = msg
        new_relation.ended_timestamp = datetime.now()

        this_session.commit()
        this_session.close()


    if(not os.path.exists(mrxsPath)):
        return fail_callback(new_relation, f'病理图文件不存在: {mrxsPath}')
    
    try:
        from PIL import Image
        Image.MAX_IMAGE_PIXELS = None
        slide = openslide.open_slide(mrxsPath)
    except openslide.OpenSlideError:
        return fail_callback(new_relation, f'打开文件 {mrxsPath} 失败，请检查文件是否正确')

    dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)

    totalNum = sum(x*y for x, y in dz_gen.level_tiles)

    new_relation.totalImages = totalNum
    this_session.commit()
    
    try:
        # assert False, "主动抛出"
        new_output_dir = os.path.join(outputDir, slide_name)
        os.makedirs(outputDir, exist_ok=True)
        os.makedirs(new_output_dir, exist_ok=True)

        currentNum = 0  
        for level in range(dz_gen.level_count):
            level_dir = os.path.join(new_output_dir, "images", str(level))
            os.makedirs(level_dir, exist_ok=True)
            tiles = dz_gen.level_tiles[level]
            
            for x in range(tiles[0]):
                for y in range(tiles[1]):
                    tile = dz_gen.get_tile(level, (x, y))
                    tile_path = os.path.join(level_dir, f'{x}_{y}.jpeg')
                    tile.save(tile_path, 'JPEG')
                    currentNum += 1
                
                new_relation.successImages = currentNum
                this_session.commit()

                        
            with open(os.path.join(new_output_dir, f'image.xml'), 'w') as f:
                f.write(dz_gen.get_dzi('JPEG'))


        new_relation.status = "success"
        new_relation.ended_timestamp = datetime.now()
        
        # task.successProjects += 1
        this_session.execute(text(
            f''' UPDATE d_tasks SET successProjects = successProjects + 1 WHERE id = {taskId}'''
        ))
    
    except Exception as e:

        print("转化失败")
        fail_callback(new_relation, traceback.format_exc())

        # task.failedProjects += 1
        this_session.execute(text(
            f'UPDATE d_tasks SET failedProjects=failedProjects+1 where id = {taskId}'
        ))

    finally:
        this_session.commit()
        this_session.close()


@bp.route('/batchCreateDeepZoomForPatho', methods=['POST'])
def batchCreateDeepZoomForPatho():

    userId = request.headers.get('Uid')


    data = request.json
    taskId = data.get('taskId')
    mrxsPaths = data.get('mrxsPaths')
    tileSize = data.get('tileSize')
    overlap = data.get('overlap')
    name = data.get('name')
    imageType = data.get('imageType')
    taskType = data.get('taskType')
    rules = data.get('rules')


    userId = request.headers.get('Uid')

    org = DOrgUser.find_by_user_id(db.session, userId)


    if org is None:
        return jsonify({"code": 1, "message": "用户不存在"})

    # print("找到的org：{}".format(org))
    orgId = org.orgId


    task = DTask.find_tasks_by_id(db.session, taskId)
    if task is None:
        return jsonify({"code": 1, "message": "任务不存在"})

    jobs = []
    for index_, mrxsPath in enumerate(mrxsPaths):

        projectId = generate_random_identifier()
        projectName = "{}_{}".format(name, index_)

        outputDir = "/nfs/dataturk/uploads/{}".format(projectId)

        rp_relation = RProjectTask(
                created_timestamp=datetime.now(),
                project=projectId,
                status="pending",
                task=taskId,
                projectName=projectName,
                parameters=json.dumps({
                    "mrxsPath": mrxsPath,
                    "tileSize": tileSize,
                    "overlap": overlap,
                })
            )
        
        db.session.add(rp_relation)
        db.session.commit()
        
        job = redis_queue.enqueue(process_mrxs, userId, rules, imageType, taskType, orgId, projectId, projectName, taskId, mrxsPath, tileSize, overlap, outputDir, job_timeout=86400)
        
        jobs.append(job)

    return jsonify({"message": len(mrxsPaths)}), 200


@bp.route('/checkTask', methods=['POST'])
def checkTask():
    data = request.json
    types = data.get('types')

    res = []
    for type in types:
        tasks = DTask.find_tasks_by_type(db.session, type)
        for task in tasks:
            relations = RProjectTask.find_relation_by_task_id(db.session, task["id"])
            # print(task, relations)
            res.extend(relations)

    return jsonify({
        "res": res
    }), 200

@bp.route('/getModelList', methods=['GET'])
def data_info():
    all_records = DModel.query.all()

    res = []
    for record in all_records:
        values = dict(record.as_dict())
        values['labels'] = [i for i in values['labels'].split(' ') if len(i) > 0] 
        values['path'] = [i for i in values['path'].split(';') if len(i) > 0]
        res.append(values)
    return jsonify({"data": res})


@bp.route('/createModel', methods=['POST'])
def create_model():
    try:
        # data = request.form.to_dict()
        images_path = ""
        files_dict = request.files
        
        data = json.loads(request.form.get('data'))
        required_fields = ['modelName', 'type', 'hasLabel', 'imageType', 'introduction', 'labels']
        for field in required_fields:
            if field not in data:
                # return jsonify({"error": f"Missing required field1: {field}"}), 601
                return jsonify(data), 601

        model_id = data.get('id')

        if model_id:
            # If an 'id' is provided, update the existing model
            existing_model = DModel.query.get(model_id)

            if not existing_model:
                return jsonify({"error": f"Model with id {model_id} not found"}), 602

            for key, file in files_dict.items():
                file_name = generate_random_identifier() + "___" + file.filename
                file.save(os.path.join('/nfs/dataturk/models', file_name))
                images_path = images_path + os.path.join('/models', file_name) + ";"
            
            # Update the fields with new values
            existing_model.modelName = data['modelName']
            existing_model.type = data['type']
            existing_model.hasLabel = data['hasLabel']
            existing_model.imageType = data['imageType']
            existing_model.introduction = data['introduction']
            existing_model.labels = ' '.join(data['labels'])
            new_path = '' if data['path'] == '' else data['path'] + ';'
            existing_model.path = new_path + images_path

            db.session.commit()

            return jsonify({"message": "Model updated successfully", "data": existing_model.as_dict()}), 200
        else:
            # If no 'id' is provided, create a new model
            if(len(files_dict) == 0):
                return jsonify({"error": "No files uploaded, at least one"}), 600
        
        
            for key, file in files_dict.items():
                file_name = generate_random_identifier() + "___" + file.filename
                file.save(os.path.join('/nfs/dataturk/models', file_name))
                images_path = images_path + os.path.join('/models', file_name) + ";"
                
            new_model = DModel(
                modelName=data['modelName'],
                type=data['type'],
                hasLabel=data['hasLabel'],
                imageType=data['imageType'],
                fixedImageSize=False,
                requiredImageWidth='',
                requiredImageHeight='',
                introduction=data['introduction'],
                labels=' '.join(data['labels']),
                path=images_path
            )

            db.session.add(new_model)
            db.session.commit()

            return jsonify({"message": "Model created successfully", "data": new_model.as_dict()}), 200
    except Exception as e:
        return jsonify(message="函数执行错误: " + str(e)), 500


@bp.route('/deleteModel', methods=['POST'])
def delete_model():
    data = request.json
    
    modelId = data["id"]
    model_to_delete = DModel.query.get(modelId)
    if model_to_delete is None:
        return jsonify({"error": f"Model with ID {modelId} not found"}), 404

    db.session.delete(model_to_delete)
    db.session.commit()

    return jsonify({"message": f"Model with ID {modelId} deleted successfully"}), 200


@bp.route('/getTaskStatus', methods=['GET'])
def getTaskStatus():
    uid = request.headers.get('Uid')
    items = redisClient.keys(f'{uid}_*')
    userTaskList = [json.loads( redisClient.get(item) ) for item in items]
    userTaskList.sort(key=lambda x: x['CreateTime'], reverse=True)    
    return jsonify(userTaskList)


@bp.route('/createNewProjectTask', methods=['POST'])
def createNewProjectTask():
    userId = request.headers.get('Uid')

    if userId is None:
        return jsonify({
        "error": 1,
        "msg": "uid not given"
    })

    data = request.json

    name = data["name"]
    totalProjects = data["totalProjects"]
    type = data['type']

    current = datetime.now()

    
    task = DTask(
        created_timestamp=current,
        ended_timestamp=current,
        type=type,
        name=name,
        totalProjects=totalProjects,
        successProjects=0,
        failedProjects=0,
        userId=userId,
    )

    db.session.add(task)
    db.session.commit()

    return jsonify({
        "response": task.id
    })


@bp.route('/getAllTasks', methods=['POST'])
def getAllTasks():
    userId = request.headers.get('Uid')

    if userId is None:
        return jsonify({
        "error": 1,
        "msg": "uid not given"
    })

    data = request.json
    type = data['type']

    return jsonify(DTask.find_tasks_by_type_and_uid(db.session, userId, type))

@bp.route('/getMrxsProgress', methods=['POST'])
def getMrxsProgress():
    data = request.json
    taskId = data['taskId']

    return jsonify(RProjectTask.find_relation_by_task_id(db.session, taskId))


@bp.route('/recreateProject', methods=['POST'])
def recreateProject():
    data = request.json
    projectId = data['projectId']
    mrxsPath = data['mrxsPath']
    taskId = data['taskId']

    record = RProjectTask.find_relation_by_project_id(db.session, projectId)

    kwargs = json.loads(record.parameters)

    process = Process(target=process_mrxs, 
        args=(
            None,
            None,
            None, 
            None,
            None,
            projectId, 
            None,  
            taskId, 
            mrxsPath, 
            kwargs['tileSize'], 
            kwargs['overlap'], 
            "/nfs/dataturk/uploads/{}".format(projectId), 
            True     
        ))
    process.start()

    return jsonify({
        "response": projectId,  
    })

@bp.route('/test', methods=['POST'])
def test():
    return jsonify({
        "response": "这是一个热部署测试"
    })