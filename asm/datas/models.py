
from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, BigInteger, DateTime, Text, Boolean, Float


class d_Projects(db.Model):
    __tablename__ = 'd_projects'  # 指定数据库中表的名称

    id = Column(Integer, primary_key=True, autoincrement=True)
    HITRepeatCount = Column(Integer, nullable=False, default=0)
    accessType = Column(String(255), nullable=False, default='RESTRICTED')
    created_timestamp = Column(DateTime, nullable=False)
    description = Column(String(255), nullable=True)
    imageOrganType = Column(String(255), nullable=True)
    isPublic = Column(Boolean, nullable=False)
    labelsDone = Column(Integer, nullable=False)
    medicalImageFormat = Column(String(255), nullable=True)
    minGoldenHITs = Column(Integer, nullable=False)
    name = Column(String(255), nullable=False)
    notes = Column(String(255), nullable=True)
    orgId = Column(String(255), nullable=False)
    shortDescription = Column(String(255), nullable=True)
    status = Column(String(255), nullable=False)
    subtitle = Column(String(255), nullable=True)
    taskRules = Column(String(255), nullable=False)
    taskType = Column(String(255), nullable=False)
    totalStorageInMBs = Column(Integer, nullable=False, default=0)
    updated_timestamp = Column(DateTime, nullable=False)
    validateWithGoldenHITs = Column(Boolean, nullable=False)
    imageType = Column(String(255), nullable=False)
    mrxsAddress = Column(String(255), nullable=True)
    totalHits = Column(Integer)
    totalDoneHits = Column(Integer)
    
    
    @classmethod
    def query_project(cls, id):
        # 如果没有匹配的结果，first()方法将返回 None，
        # 如果有匹配的结果，将返回一个结果对象。
        # 你可以根据返回值是否为 None 来判断是否有查询的数据。
        return cls.query.filter_by(id=id).first()

class d_Hits_Result(db.Model):
    
    __tablename__ = 'd_hits_result'  # 指定数据库中表的名称
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    created_timestamp = Column(DateTime, nullable=False)
    hitId = Column(BigInteger, nullable=False)
    model = Column(String(255), nullable=False)
    notes = Column(String(255), nullable=False)
    predLabel = Column(Text, nullable=False)
    projectId = Column(String(255), nullable=False)
    result = Column(Text, nullable=False)
    status = Column(Text, nullable=False)
    timeTakenToLabelInSec = Column(Integer, nullable=False)
    updated_timestamp = Column(DateTime, nullable=False)
    userId = Column(String(255), nullable=False)

    def __init__(self, hitId, model, notes, predLabel, projectId, result, status, timeTakenToLabelInSec, userId, updated_timestamp, created_timestamp):
        self.hitId = hitId
        self.model = model
        self.notes = notes
        self.userId = userId
        self.result = result
        self.status = status
        self.predLabel = predLabel
        self.projectId = projectId
        self.updated_timestamp = updated_timestamp
        self.created_timestamp = created_timestamp
        self.timeTakenToLabelInSec = timeTakenToLabelInSec

    def query_hit_result(self):
        # 如果没有匹配的结果，first()方法将返回 None，
        # 如果有匹配的结果，将返回一个结果对象。
        # 你可以根据返回值是否为 None 来判断是否有查询的数据。
        return self.query.filter_by(hitId=self.hitId, model=self.model, userId=self.userId).first()            

    def update_hit_result(self):
        obj = self.query_hit_result()
        
        if obj:   # 更新
            obj.status = self.status
            obj.result = self.result
            obj.predLabel = self.predLabel
            obj.updated_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            db.session.commit()
        else:     # 插入
            db.session.add(self)
            db.session.commit()

    def __repr__(self):
        return f'userId {self.userId} hitId {self.hitId}, model {self.model}, predLabel {self.predLabel}, projectId {self.projectId}, created_timestamp {self.created_timestamp}, updated_timestamp {self.updated_timestamp}'



class d_Hits(db.Model):
    
    __tablename__ = 'd_hits'  # 指定数据库中表的名称
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    compressFactor = Column(Integer, nullable=False)
    correctResult = Column(String(255), nullable=True)
    data = Column(Text, nullable=False)
    evaluation = Column(Text, nullable=True)
    extras = Column(Text, nullable=True)
    goldenHITResultId = Column(Integer, nullable=False)
    isGoldenHIT = Column(Text, nullable=False)
    isURL = Column(Text, nullable=False)
    notes = Column(String(255), nullable=True)
    projectId = Column(String(255), nullable=False)
    status = Column(Text, nullable=False)
    created_timestamp = Column(DateTime, nullable=False)
    updated_timestamp = Column(DateTime, nullable=False)

    # def __init__(self, compressFactor, correctResult, data, evaluation, extras, goldenHITResultId, isGoldenHIT, isURL, projectId, status, created_timestamp, updated_timestamp):
    #     self.compressFactor = compressFactor
    #     self.correctResult = correctResult
    #     self.data = data
    #     self.evaluation = evaluation
    #     self.extras = extras
    #     self.goldenHITResultId = goldenHITResultId
    #     self.isGoldenHIT = isGoldenHIT
    #     self.isURL = isURL
    #     self.projectId = projectId
    #     self.status = status
    #     self.updated_timestamp = updated_timestamp
    #     self.created_timestamp = created_timestamp

    @classmethod
    def query_hit_by_projectId(cls, projectId):
        return cls.query.filter_by(projectId=projectId).all()         

    def __repr__(self):
        return f'projectId {self.projectId}, created_timestamp {self.created_timestamp}, updated_timestamp {self.updated_timestamp}'

class DModel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    modelName = db.Column(db.String(255), nullable=False)
    type = db.Column(db.Integer, nullable=False)
    hasLabel = db.Column(db.Boolean, nullable=False)
    imageType = db.Column(db.String(255), nullable=False)
    fixedImageSize = db.Column(db.Boolean, nullable=False)
    requiredImageWidth = db.Column(db.Integer, nullable=False)
    requiredImageHeight = db.Column(db.Integer, nullable=False)
    introduction = db.Column(db.Text, nullable=True)  # Assuming Text type for introduction
    labels = db.Column(db.Text, nullable=True)  # Assuming Text type for introduction
    path = db.Column(db.String(255))

    def as_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


class DTask(db.Model):
    __tablename__ = 'd_tasks'

    id = Column(Integer, primary_key=True)
    created_timestamp = Column(DateTime)
    ended_timestamp = Column(DateTime)
    type = Column(String(10))
    userId = Column(String(255))
    successProjects = Column(Integer)
    failedProjects = Column(Integer)
    totalProjects = Column(Integer)
    name = Column(String(255))

    def as_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}

    @classmethod
    def find_tasks_by_type(self, session, task_type):
        tasks = session.query(DTask).filter(DTask.type == task_type).all()
        return [task.as_dict() for task in tasks]

    @classmethod
    def find_tasks_by_id(self, session, id):
        return session.query(DTask).filter(DTask.id == id).first()


    @classmethod
    def find_tasks_by_type_and_uid(cls, session, user_id, task_type):
        tasks = session.query(DTask).filter(DTask.userId == user_id and DTask.type == task_type).all()
        return [task.as_dict() for task in tasks]

class RProjectTask(db.Model):
    __tablename__ = 'r_project_task'

    id = Column(Integer, primary_key=True)
    created_timestamp = Column(DateTime)
    ended_timestamp = Column(DateTime)
    project = Column(String(255))
    status = Column(String(255))
    task = Column(Integer)
    projectName = Column(String(255))
    message = Column(String(255))
    successImages = Column(Integer, default=0)
    failedImages = Column(Integer, default=0)
    totalImages = Column(Integer, default=0)
    parameters = Column(Text, default='')

    def as_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}

    @classmethod
    def find_relation_by_task_id(self, session, task_id):
        tasks = session.query(RProjectTask).filter(RProjectTask.task == task_id).all()
        return [task.as_dict() for task in tasks]

    @classmethod
    def find_relation_by_project_id(self, session, project_id):
        return session.query(RProjectTask).filter(RProjectTask.project == project_id).first()


class DOrgUser(db.Model):
    __tablename__ = 'd_org_users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    created_timestamp = Column(DateTime)
    notes = Column(String(255))
    orgId = Column(String(255))
    role = Column(String(255))
    updated_timestamp = Column(DateTime)
    userId = Column(String(255))

    def as_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


    @classmethod
    def find_by_user_id(cls, session, user_id):
        user = session.query(cls).filter(cls.userId == user_id).first()
        return user


class DProjectUser(db.Model):
    __tablename__ = 'd_project_users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    created_timestamp = Column(DateTime)
    notes = Column(String(255))
    projectId = Column(String(255))
    role = Column(String(255))
    updated_timestamp = Column(DateTime)
    userId = Column(String(255))

    def as_dict(self):
        # Convert the SQLAlchemy model instance to a dictionary
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


