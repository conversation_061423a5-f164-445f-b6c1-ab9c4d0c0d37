
import os
import pynvml
import logging

logger = logging.getLogger('app.gpu')

def seek_gpu():
    choose_gid = 0
    max_free_memory = 0
    pynvml.nvmlInit()
    GPUCount = pynvml.nvmlDeviceGetCount()
    gid = 1
    handle = pynvml.nvmlDeviceGetHandleByIndex(gid)    # GPU的id
    meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
    max_free_memory = meminfo.free / 1024**3
    # for gid in range(GPUCount):
    #     handle = pynvml.nvmlDeviceGetHandleByIndex(gid)    # GPU的id
    #     meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
    #     if max_free_memory < meminfo.free / 1024**3:
    #         max_free_memory = meminfo.free / 1024**3
    #         choose_gid = gid
    print('选择 GPU {}, 空置显存 {:.2f} GB'.format(gid, max_free_memory))
    logger.info('选择 GPU {}, 空置显存 {:.2f} GB'.format(gid, max_free_memory))
    os.environ["CUDA_VISIBLE_DEVICES"] = str(choose_gid)
    pynvml.nvmlShutdown()