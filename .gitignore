# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
*.egg
*.egg-info/
dist/
build/
.Python
env/
venv/
.venv/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS 文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
log/
log/*.log
log/*.pid
app.log
app/*.log
nohup.out

# Celery 相关
celerybeat-schedule
celerybeat.pid

# 环境变量
.env
.env.local
.env.*.local

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 忽略 git 本身的元数据
.git/