#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主目录脚本重组验证脚本

验证主目录脚本重组后所有功能是否正常工作
"""

import os
import sys
import subprocess
from pathlib import Path


def test_directory_structure():
    """测试目录结构"""
    print("🔍 测试目录结构...")
    
    required_dirs = [
        'config',
        'scripts',
        'scripts/celery',
        'scripts/deployment',
        'tools',
        'tests/integration',
        'tests/manual'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
        else:
            print(f"✅ 目录存在: {dir_name}")
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    
    return True


def test_file_locations():
    """测试文件位置"""
    print("\n🔍 测试文件位置...")
    
    required_files = [
        'config/__init__.py',
        'config/config.py',
        'scripts/__init__.py',
        'scripts/README.md',
        'scripts/celery/start_celery.sh',
        'scripts/celery/start_celery_dev.sh',
        'scripts/celery/start_celery_prod.sh',
        'scripts/celery/stop_celery.sh',
        'scripts/celery/stop_celery_dev.sh',
        'scripts/celery/stop_celery_prod.sh',
        'tools/__init__.py',
        'tools/README.md',
        'tools/setup_ssh.py',
        'tests/integration/__init__.py',
        'tests/integration/README.md',
        'tests/integration/test_celery_task.py',
        'tests/integration/test_image_task.py',
        'tests/integration/test_rabbitmq_sender.py',
        'tests/test_requirements.txt'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
        else:
            print(f"✅ 文件存在: {file_name}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    return True


def test_script_permissions():
    """测试脚本权限"""
    print("\n🔍 测试脚本权限...")
    
    script_files = [
        'scripts/celery/start_celery.sh',
        'scripts/celery/start_celery_dev.sh',
        'scripts/celery/start_celery_prod.sh',
        'scripts/celery/stop_celery.sh',
        'scripts/celery/stop_celery_dev.sh',
        'scripts/celery/stop_celery_prod.sh',
        'tools/setup_ssh.py'
    ]
    
    for script_file in script_files:
        if os.path.exists(script_file):
            # 检查是否有执行权限
            if os.access(script_file, os.X_OK):
                print(f"✅ 脚本可执行: {script_file}")
            else:
                print(f"❌ 脚本不可执行: {script_file}")
                return False
        else:
            print(f"❌ 脚本不存在: {script_file}")
            return False
    
    return True


def test_config_imports():
    """测试配置导入"""
    print("\n🔍 测试配置导入...")
    
    try:
        # 测试配置模块导入
        from config.config import RABBITMQ_HOST, REDIS_HOST
        print("✅ 配置模块导入成功")
        
        # 测试配置值
        if RABBITMQ_HOST and REDIS_HOST:
            print("✅ 配置值加载成功")
        else:
            print("❌ 配置值为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False


def test_integration_imports():
    """测试集成测试导入"""
    print("\n🔍 测试集成测试导入...")
    
    test_modules = [
        'tests.integration.test_celery_task',
        'tests.integration.test_image_task',
        'tests.integration.test_rabbitmq_sender'
    ]
    
    for module_name in test_modules:
        try:
            __import__(module_name)
            print(f"✅ 模块导入成功: {module_name}")
        except Exception as e:
            print(f"❌ 模块导入失败: {module_name} - {e}")
            return False
    
    return True


def test_old_files_removed():
    """测试旧文件是否已移除"""
    print("\n🔍 测试旧文件是否已移除...")
    
    old_files = [
        'config.py',
        'setup_ssh.py',
        'test_celery_task.py',
        'test_image_task.py',
        'test_rabbitmq_sender.py',
        'start_celery.sh',
        'start_celery_dev.sh',
        'start_celery_prod.sh',
        'stop_celery.sh',
        'stop_celery_dev.sh',
        'stop_celery_prod.sh'
    ]
    
    remaining_files = []
    for file_name in old_files:
        if os.path.exists(file_name):
            remaining_files.append(file_name)
        else:
            print(f"✅ 旧文件已移除: {file_name}")
    
    if remaining_files:
        print(f"⚠️ 仍存在旧文件: {remaining_files}")
        print("   这些文件可能是软链接或备份，请手动检查")
    
    return True


def test_app_functionality():
    """测试应用功能"""
    print("\n🔍 测试应用功能...")
    
    try:
        # 测试应用模块导入
        import app
        print("✅ 应用模块导入成功")
        
        # 测试配置加载
        if hasattr(app, 'celery'):
            print("✅ Celery实例创建成功")
        else:
            print("❌ Celery实例创建失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 应用功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 主目录脚本重组验证")
    print("=" * 60)
    
    tests = [
        ("目录结构测试", test_directory_structure),
        ("文件位置测试", test_file_locations),
        ("脚本权限测试", test_script_permissions),
        ("配置导入测试", test_config_imports),
        ("集成测试导入测试", test_integration_imports),
        ("旧文件清理测试", test_old_files_removed),
        ("应用功能测试", test_app_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！主目录脚本重组成功！")
        print("\n💡 下一步:")
        print("   1. 测试Celery脚本: ./scripts/celery/start_celery_dev.sh")
        print("   2. 运行集成测试: python tests/integration/test_celery_task.py")
        print("   3. 使用工具脚本: python tools/setup_ssh.py")
        print("   4. 更新任何外部引用的路径")
        return True
    else:
        print("❌ 部分测试失败，请检查问题并修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
