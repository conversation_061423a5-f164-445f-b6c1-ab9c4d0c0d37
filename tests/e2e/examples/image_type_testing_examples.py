#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像类型测试示例

展示如何使用重构后的任务发送器测试所有图像类型（1-4）
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from tests.e2e.core.senders import (
    ImageTaskSender, 
    send_image_task, 
    send_multiple_image_tasks,
    get_supported_image_types,
    list_available_urls
)
from tests.e2e.config import E2ETestConfig


def example_basic_usage():
    """示例：基本使用方式"""
    print("🎯 示例1：基本使用方式")
    print("=" * 50)
    
    # 1. 使用默认参数（病理图像）
    print("📤 发送默认任务（病理图像）...")
    success = send_image_task()
    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 2. 发送不同类型的图像任务
    image_types = [1, 2, 3, 4]
    type_names = ["普通图像", "DICOM图像", "病理图像", "多通道图像"]
    
    for image_type, type_name in zip(image_types, type_names):
        print(f"📤 发送{type_name}任务 (类型={image_type})...")
        success = send_image_task(image_type_id=image_type)
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
        time.sleep(1)  # 避免任务ID冲突


def example_url_selection():
    """示例：URL选择功能"""
    print("\n🎯 示例2：URL选择功能")
    print("=" * 50)
    
    # 1. 查看支持的图像类型
    print("📋 支持的图像类型:")
    supported_types = get_supported_image_types()
    for type_id, config in supported_types.items():
        print(f"   类型 {type_id}: {config['name']}")
        print(f"     支持格式: {', '.join(config['extensions'])}")
        print(f"     默认URL数量: {len(config['default_urls'])}")
    
    # 2. 查看特定类型的所有可用URL
    print(f"\n📋 病理图像(类型3)的可用URL:")
    pathology_urls = list_available_urls(3)
    for i, url in enumerate(pathology_urls):
        print(f"   URL[{i}]: {url}")
    
    # 3. 使用不同的URL索引发送任务
    print(f"\n📤 使用不同URL发送病理图像任务:")
    for i in range(min(3, len(pathology_urls))):  # 最多测试3个URL
        print(f"   使用URL[{i}]发送任务...")
        success = send_image_task(image_type_id=3, url_index=i)
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
        time.sleep(1)


def example_custom_parameters():
    """示例：自定义参数"""
    print("\n🎯 示例3：自定义参数")
    print("=" * 50)
    
    # 1. 使用自定义URL
    print("📤 使用自定义URL发送任务...")
    custom_url = "/custom/path/my_special_image.svs"
    success = send_image_task(
        image_type_id=3,
        image_url=custom_url,
        project_id="99",
        image_name="custom_test_image",
        metadata={
            "custom_test": True,
            "description": "使用自定义参数的测试"
        }
    )
    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 2. 使用ImageTaskSender类进行更复杂的操作
    print("\n📤 使用ImageTaskSender类...")
    config = E2ETestConfig('dev')
    sender = ImageTaskSender(config)
    
    # 获取特定类型的信息
    dicom_info = sender.get_image_type_info(2)
    if dicom_info:
        print(f"   DICOM图像信息: {dicom_info['description']}")
        print(f"   支持的扩展名: {', '.join(dicom_info['extensions'])}")
    
    # 创建任务数据但不发送
    task_data = sender.create_task_data(
        image_type_id=2,
        project_id="88",
        url_index=1,
        metadata={"test_mode": "advanced"}
    )
    print(f"   创建的任务ID: {task_data['taskId']}")
    print(f"   使用的图像URL: {task_data['imageUrl']}")


def example_batch_testing():
    """示例：批量测试"""
    print("\n🎯 示例4：批量测试")
    print("=" * 50)
    
    # 1. 批量发送同一类型的任务
    print("📤 批量发送普通图像任务...")
    results = send_multiple_image_tasks(
        count=3,
        image_type_id=1,
        project_id="77"
    )
    success_count = sum(results)
    print(f"   结果: {success_count}/{len(results)} 成功")
    
    # 2. 批量发送不同类型的任务
    print("\n📤 批量发送混合类型任务...")
    sender = ImageTaskSender()
    
    mixed_tasks = [
        {"image_type_id": 1, "project_id": "66"},
        {"image_type_id": 2, "project_id": "66", "url_index": 1},
        {"image_type_id": 3, "project_id": "66", "url_index": 2},
        {"image_type_id": 4, "project_id": "66"}
    ]
    
    mixed_results = []
    for i, task_params in enumerate(mixed_tasks):
        print(f"   发送任务 {i+1}/{len(mixed_tasks)} (类型={task_params['image_type_id']})...")
        success = sender.send_task(**task_params)
        mixed_results.append(success)
        print(f"     结果: {'✅ 成功' if success else '❌ 失败'}")
        time.sleep(1)
    
    mixed_success_count = sum(mixed_results)
    print(f"   混合任务结果: {mixed_success_count}/{len(mixed_results)} 成功")


def example_error_handling():
    """示例：错误处理"""
    print("\n🎯 示例5：错误处理")
    print("=" * 50)
    
    sender = ImageTaskSender()
    
    # 1. 测试不支持的图像类型
    print("📤 测试不支持的图像类型...")
    try:
        task_data = sender.create_task_data(image_type_id=99)
        print("   ❌ 应该抛出异常但没有")
    except ValueError as e:
        print(f"   ✅ 正确捕获异常: {e}")
    
    # 2. 测试超出范围的URL索引
    print("\n📤 测试超出范围的URL索引...")
    try:
        task_data = sender.create_task_data(image_type_id=1, url_index=999)
        print(f"   ⚠️ 使用了默认URL: {task_data['imageUrl']}")
    except Exception as e:
        print(f"   ❌ 意外异常: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("🎯 图像类型测试示例")
    print("=" * 60)
    print("展示如何使用重构后的任务发送器测试所有图像类型")
    print("=" * 60)
    
    try:
        # 运行所有示例
        example_basic_usage()
        example_url_selection()
        example_custom_parameters()
        example_batch_testing()
        example_error_handling()
        
        print("\n✅ 所有示例运行完成!")
        print("\n📝 使用提示:")
        print("   1. 可以通过 image_type_id 参数指定图像类型 (1-4)")
        print("   2. 可以通过 url_index 参数选择不同的默认URL")
        print("   3. 可以通过 image_url 参数使用完全自定义的路径")
        print("   4. 所有参数都是可选的，有合理的默认值")
        
    except KeyboardInterrupt:
        print("\n⚠️ 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
