#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后任务发送器的测试脚本

验证重构后的ImageTaskSender是否正常工作
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from tests.e2e.core.senders import (
    ImageTaskSender,
    send_image_task,
    get_supported_image_types,
    list_available_urls
)
from tests.e2e.config import E2ETestConfig


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试1: 基本功能")
    print("-" * 40)
    
    try:
        # 测试获取支持的图像类型
        supported_types = get_supported_image_types()
        print(f"✅ 获取支持的图像类型: {len(supported_types)} 种")
        
        for type_id, config in supported_types.items():
            print(f"   类型 {type_id}: {config['name']} ({len(config['default_urls'])} 个URL)")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def test_url_listing():
    """测试URL列表功能"""
    print("\n🧪 测试2: URL列表功能")
    print("-" * 40)
    
    try:
        # 测试每种图像类型的URL列表
        for image_type_id in [1, 2, 3, 4]:
            urls = list_available_urls(image_type_id)
            print(f"✅ 图像类型 {image_type_id} 有 {len(urls)} 个URL")
            
            # 显示前2个URL作为示例
            for i, url in enumerate(urls[:2]):
                print(f"   URL[{i}]: {url}")
        
        return True
    except Exception as e:
        print(f"❌ URL列表测试失败: {e}")
        return False


def test_task_data_creation():
    """测试任务数据创建"""
    print("\n🧪 测试3: 任务数据创建")
    print("-" * 40)
    
    try:
        sender = ImageTaskSender()
        
        # 测试每种图像类型的任务数据创建
        for image_type_id in [1, 2, 3, 4]:
            task_data = sender.create_task_data(
                image_type_id=image_type_id,
                project_id="test_project",
                url_index=0
            )
            
            # 验证必要字段
            required_fields = ['taskId', 'imageTypeId', 'projectId', 'imageName', 'imageId', 'imageUrl']
            missing_fields = [field for field in required_fields if field not in task_data]
            
            if missing_fields:
                print(f"❌ 图像类型 {image_type_id} 缺少字段: {missing_fields}")
                return False
            
            print(f"✅ 图像类型 {image_type_id} 任务数据创建成功")
            print(f"   任务ID: {task_data['taskId']}")
            print(f"   图像URL: {task_data['imageUrl']}")
            print(f"   图像类型: {task_data['metadata']['image_type']}")
        
        return True
    except Exception as e:
        print(f"❌ 任务数据创建测试失败: {e}")
        return False


def test_url_index_selection():
    """测试URL索引选择"""
    print("\n🧪 测试4: URL索引选择")
    print("-" * 40)
    
    try:
        sender = ImageTaskSender()
        
        # 测试病理图像的不同URL索引
        image_type_id = 3
        urls = list_available_urls(image_type_id)
        
        for i in range(min(3, len(urls))):  # 测试前3个URL
            task_data = sender.create_task_data(
                image_type_id=image_type_id,
                url_index=i
            )
            
            expected_url = urls[i]
            actual_url = task_data['imageUrl']
            
            if actual_url == expected_url:
                print(f"✅ URL索引 {i} 选择正确: {actual_url}")
            else:
                print(f"❌ URL索引 {i} 选择错误:")
                print(f"   期望: {expected_url}")
                print(f"   实际: {actual_url}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ URL索引选择测试失败: {e}")
        return False


def test_custom_url():
    """测试自定义URL"""
    print("\n🧪 测试5: 自定义URL")
    print("-" * 40)
    
    try:
        sender = ImageTaskSender()
        
        custom_url = "/custom/test/path/image.svs"
        task_data = sender.create_task_data(
            image_type_id=3,
            image_url=custom_url
        )
        
        if task_data['imageUrl'] == custom_url:
            print(f"✅ 自定义URL设置成功: {custom_url}")
            return True
        else:
            print(f"❌ 自定义URL设置失败:")
            print(f"   期望: {custom_url}")
            print(f"   实际: {task_data['imageUrl']}")
            return False
            
    except Exception as e:
        print(f"❌ 自定义URL测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试6: 错误处理")
    print("-" * 40)
    
    try:
        sender = ImageTaskSender()
        
        # 测试不支持的图像类型
        try:
            task_data = sender.create_task_data(image_type_id=99)
            print("❌ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✅ 正确捕获不支持图像类型异常: {e}")
        
        # 测试超出范围的URL索引（应该自动使用索引0）
        task_data = sender.create_task_data(
            image_type_id=1,
            url_index=999
        )
        
        # 应该使用第一个URL
        expected_url = list_available_urls(1)[0]
        if task_data['imageUrl'] == expected_url:
            print("✅ 超出范围的URL索引正确处理（使用默认索引0）")
        else:
            print("❌ 超出范围的URL索引处理错误")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试7: 向后兼容性")
    print("-" * 40)
    
    try:
        # 测试原有的调用方式是否仍然有效
        
        # 1. 无参数调用（应该使用默认值）
        task_data1 = ImageTaskSender().create_task_data()
        if task_data1['imageTypeId'] == 3:  # 默认病理图像
            print("✅ 无参数调用向后兼容")
        else:
            print("❌ 无参数调用向后兼容失败")
            return False
        
        # 2. 只指定image_type_id
        task_data2 = ImageTaskSender().create_task_data(image_type_id=2)
        if task_data2['imageTypeId'] == 2:
            print("✅ 指定image_type_id向后兼容")
        else:
            print("❌ 指定image_type_id向后兼容失败")
            return False
        
        # 3. 指定project_id
        task_data3 = ImageTaskSender().create_task_data(project_id="99")
        if task_data3['projectId'] == "99":
            print("✅ 指定project_id向后兼容")
        else:
            print("❌ 指定project_id向后兼容失败")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便捷函数"""
    print("\n🧪 测试8: 便捷函数")
    print("-" * 40)
    
    try:
        # 注意：这里只测试函数调用，不实际发送任务
        
        # 测试get_supported_image_types便捷函数
        types = get_supported_image_types()
        if len(types) == 4:  # 应该有4种图像类型
            print("✅ get_supported_image_types便捷函数正常")
        else:
            print("❌ get_supported_image_types便捷函数异常")
            return False
        
        # 测试list_available_urls便捷函数
        urls = list_available_urls(3)
        if len(urls) > 0:
            print("✅ list_available_urls便捷函数正常")
        else:
            print("❌ list_available_urls便捷函数异常")
            return False
        
        print("ℹ️ send_image_task和send_multiple_image_tasks需要实际的RabbitMQ连接，跳过测试")
        
        return True
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 重构后任务发送器测试")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_url_listing,
        test_task_data_creation,
        test_url_index_selection,
        test_custom_url,
        test_error_handling,
        test_backward_compatibility,
        test_convenience_functions
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            failed += 1
    
    print(f"\n📊 测试结果:")
    print(f"   通过: {passed}")
    print(f"   失败: {failed}")
    print(f"   总计: {passed + failed}")
    
    if failed == 0:
        print("\n✅ 所有测试通过！重构成功！")
        return True
    else:
        print(f"\n❌ 有 {failed} 个测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
