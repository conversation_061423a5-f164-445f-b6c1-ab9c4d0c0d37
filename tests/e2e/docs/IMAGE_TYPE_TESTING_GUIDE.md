# 图像类型测试指南

本指南介绍如何使用重构后的端到端测试框架测试所有图像类型（1-4）。

## 🎯 重构改进

### 解决的问题
- ❌ **硬编码问题**: 之前的 `image_type_id`、`project_id`、`image_url` 都是硬编码
- ❌ **不灵活**: 测试不同图像类型需要修改源代码
- ❌ **维护困难**: 添加新的测试文件路径需要修改多处代码

### 新功能特性
- ✅ **灵活配置**: 支持所有图像类型的动态配置
- ✅ **多URL支持**: 每种图像类型提供多个默认文件路径
- ✅ **向后兼容**: 保持现有调用方式不变
- ✅ **易于扩展**: 新增图像类型只需修改配置字典

## 📋 支持的图像类型

| 类型ID | 图像类型 | 支持格式 | 默认URL数量 | 描述 |
|--------|----------|----------|-------------|------|
| 1 | 普通图像 | `.png`, `.jpg`, `.jpeg` | 3 | 常见的自然图像格式 |
| 2 | DICOM图像 | `.dcm`, `.dicom` | 3 | DICOM医学影像标准格式 |
| 3 | 病理图像 | `.svs`, `.tif`, `.tiff`, `.kfb` | 4 | 数字病理切片图像 |
| 4 | 多通道图像 | `.qptiff`, `.tiff` | 3 | 量化病理图像和多重荧光图像 |

## 🚀 使用方法

### 1. 基本使用

```python
from tests.e2e.core.senders import send_image_task

# 发送默认任务（病理图像）
send_image_task()

# 发送普通图像任务
send_image_task(image_type_id=1)

# 发送DICOM图像任务
send_image_task(image_type_id=2)

# 发送多通道图像任务
send_image_task(image_type_id=4)
```

### 2. URL选择

```python
# 使用第一个默认URL（默认）
send_image_task(image_type_id=3, url_index=0)

# 使用第二个默认URL
send_image_task(image_type_id=3, url_index=1)

# 使用自定义URL
send_image_task(
    image_type_id=3,
    image_url="/custom/path/my_image.svs"
)
```

### 3. 完整参数示例

```python
send_image_task(
    image_type_id=2,           # DICOM图像
    project_id="88",           # 项目ID
    url_index=1,               # 使用第二个默认URL
    image_name="test_dicom",   # 自定义图像名称
    metadata={                 # 自定义元数据
        "test_type": "custom",
        "description": "DICOM测试"
    }
)
```

### 4. 批量测试

```python
from tests.e2e.core.senders import send_multiple_image_tasks

# 批量发送同类型任务
send_multiple_image_tasks(
    count=5,
    image_type_id=1,
    project_id="77"
)

# 使用ImageTaskSender类进行更复杂的批量操作
from tests.e2e.core.senders import ImageTaskSender

sender = ImageTaskSender()

# 发送混合类型任务
mixed_tasks = [
    {"image_type_id": 1, "project_id": "66"},
    {"image_type_id": 2, "project_id": "66", "url_index": 1},
    {"image_type_id": 3, "project_id": "66", "url_index": 2},
    {"image_type_id": 4, "project_id": "66"}
]

for task_params in mixed_tasks:
    sender.send_task(**task_params)
```

## 🔧 命令行使用

### 测试特定图像类型

```bash
# 测试普通图像
python tests/e2e/run_tests.py --single-task --image-type 1 --env dev

# 测试DICOM图像
python tests/e2e/run_tests.py --single-task --image-type 2 --env dev

# 测试病理图像
python tests/e2e/run_tests.py --single-task --image-type 3 --env dev

# 测试多通道图像
python tests/e2e/run_tests.py --single-task --image-type 4 --env dev
```

### 批量测试

```bash
# 批量测试病理图像（5个任务）
python tests/e2e/run_tests.py --batch-only --image-type 3 --batch-count 5 --env dev

# 完整测试（包含所有验证和批量测试）
python tests/e2e/run_tests.py --env dev

# 完整测试但跳过批量测试
python tests/e2e/run_tests.py --env dev --no-batch
```

## 🔍 配置验证

### 验证图像文件路径

在运行测试之前，建议先验证配置的图像文件路径是否存在：

```bash
# 运行配置验证工具
python tests/e2e/utils/image_config_validator.py
```

验证工具会检查：
- 所有配置的图像文件路径是否存在
- 文件是否可读
- 文件大小信息
- 提供配置建议

### 查看支持的图像类型

```python
from tests.e2e.core.senders import get_supported_image_types

# 获取所有支持的图像类型
types = get_supported_image_types()
for type_id, config in types.items():
    print(f"类型 {type_id}: {config['name']}")
    print(f"  支持格式: {config['extensions']}")
    print(f"  默认URL: {len(config['default_urls'])} 个")
```

### 查看特定类型的URL

```python
from tests.e2e.core.senders import list_available_urls

# 查看病理图像的所有可用URL
urls = list_available_urls(3)
for i, url in enumerate(urls):
    print(f"URL[{i}]: {url}")
```

## 📝 示例脚本

### 运行完整示例

```bash
# 运行图像类型测试示例
python tests/e2e/examples/image_type_testing_examples.py
```

这个示例脚本展示了：
- 基本使用方式
- URL选择功能
- 自定义参数
- 批量测试
- 错误处理

## ⚙️ 自定义配置

### 修改默认URL

如果需要修改默认的图像文件路径，编辑 `tests/e2e/core/senders/task_sender.py` 中的 `IMAGE_TYPE_CONFIGS`：

```python
IMAGE_TYPE_CONFIGS = {
    1: {  # 普通图像
        'name': '普通图像',
        'extensions': ['.png', '.jpg', '.jpeg'],
        'default_urls': [
            '/your/custom/path/image1.png',  # 修改为你的路径
            '/your/custom/path/image2.jpg',
            '/your/custom/path/image3.jpeg'
        ],
        'description': '常见的自然图像格式'
    },
    # ... 其他类型
}
```

### 添加新的图像类型

```python
IMAGE_TYPE_CONFIGS[5] = {  # 新的图像类型
    'name': '新图像类型',
    'extensions': ['.new_ext'],
    'default_urls': [
        '/path/to/new/image1.new_ext',
        '/path/to/new/image2.new_ext'
    ],
    'description': '新的图像格式描述'
}
```

## 🐛 故障排除

### 常见问题

1. **图像文件不存在**
   ```
   解决方案: 运行配置验证工具检查文件路径
   python tests/e2e/utils/image_config_validator.py
   ```

2. **不支持的图像类型**
   ```
   错误: ValueError: 不支持的图像类型ID: 5
   解决方案: 使用支持的类型ID (1-4) 或添加新类型配置
   ```

3. **URL索引超出范围**
   ```
   行为: 自动使用索引0的URL，并记录警告日志
   解决方案: 使用有效的url_index值
   ```

### 调试技巧

1. **启用详细日志**
   ```python
   config = E2ETestConfig('dev')
   config.test_config['log_level'] = 'DEBUG'
   ```

2. **检查任务数据**
   ```python
   sender = ImageTaskSender()
   task_data = sender.create_task_data(image_type_id=1)
   print(f"生成的任务数据: {task_data}")
   ```

3. **验证URL可访问性**
   ```python
   from tests.e2e.utils.image_config_validator import ImageConfigValidator
   validator = ImageConfigValidator()
   result = validator.validate_single_url('/path/to/image.png')
   print(f"URL验证结果: {result}")
   ```

## 📞 支持

如有问题或建议：
1. 查看测试日志文件 (`tests/e2e/logs/`)
2. 运行配置验证工具
3. 检查图像文件路径和权限
4. 联系开发团队
