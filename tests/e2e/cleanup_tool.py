#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NFS测试文件清理工具

独立的清理工具，用于管理端到端测试生成的文件
"""

import sys
import os
import argparse
import logging
from typing import List

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from tests.e2e.core.cleaners import (
    NFSCleaner,
    preview_nfs_cleanup,
    cleanup_nfs_test_files,
    cleanup_specific_test_images
)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='NFS测试文件清理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --preview --project-id 17                    # 预览项目17的测试文件
  %(prog)s --cleanup --project-id 17 --confirm          # 清理项目17的所有测试文件
  %(prog)s --cleanup --project-id 17 --max-age 24 --confirm  # 清理超过24小时的测试文件
  %(prog)s --cleanup-images img1 img2 --project-id 17 --confirm  # 清理指定图像
  %(prog)s --scan --project-id 17                       # 扫描项目17的测试文件
        """
    )
    
    # 基本参数
    parser.add_argument('--project-id', required=True,
                       help='项目ID')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev',
                       help='环境类型 (默认: dev)')
    
    # 操作类型
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--preview', action='store_true',
                             help='预览清理操作（不实际删除）')
    action_group.add_argument('--cleanup', action='store_true',
                             help='执行清理操作')
    action_group.add_argument('--cleanup-images', nargs='+', metavar='IMAGE_NAME',
                             help='清理指定的图像名称')
    action_group.add_argument('--scan', action='store_true',
                             help='扫描测试文件（不删除）')
    
    # 清理参数
    parser.add_argument('--max-age', type=int,
                       help='文件最大年龄（小时），超过此时间的文件将被清理')
    parser.add_argument('--confirm', action='store_true',
                       help='确认执行删除操作（必需）')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式（等同于--preview）')
    
    # 输出控制
    parser.add_argument('--quiet', action='store_true',
                       help='减少输出信息')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    elif args.quiet:
        logging.basicConfig(level=logging.WARNING)
    else:
        logging.basicConfig(level=logging.INFO)
    
    # 显示开始信息
    if not args.quiet:
        print("🧹 NFS测试文件清理工具")
        print("=" * 50)
        print(f"项目ID: {args.project_id}")
        print(f"环境: {args.env}")
        
        if args.preview or args.dry_run:
            print("操作: 预览清理")
        elif args.cleanup:
            print("操作: 执行清理")
        elif args.cleanup_images:
            print(f"操作: 清理指定图像 ({len(args.cleanup_images)}个)")
        elif args.scan:
            print("操作: 扫描文件")
        
        if args.max_age:
            print(f"文件年龄限制: {args.max_age} 小时")
        
        print("=" * 50)
    
    try:
        # 创建清理器
        cleaner = NFSCleaner()
        
        if args.preview or args.dry_run:
            # 预览清理操作
            result = cleaner.preview_cleanup(args.project_id, args.max_age)
            
            if not args.quiet:
                if result['total_files_to_delete'] > 0:
                    print(f"\n📋 预览结果:")
                    print(f"   将删除目录数量: {result['total_files_to_delete']}")
                    print(f"   将释放空间: {result['total_size_formatted']}")
                    print(f"\n📁 将删除的目录:")
                    
                    for i, dir_info in enumerate(result['will_delete'][:10]):  # 显示前10个
                        age_str = f"{dir_info['age_hours']:.1f}小时前"
                        print(f"   {i+1:2d}. {dir_info['name']} ({dir_info['size_formatted']}, {age_str})")
                    
                    if len(result['will_delete']) > 10:
                        print(f"   ... 还有 {len(result['will_delete']) - 10} 个目录")
                    
                    print(f"\n💡 使用 --cleanup --confirm 执行实际清理操作")
                else:
                    print("\n📋 没有找到需要清理的测试文件")
        
        elif args.cleanup:
            # 执行清理操作
            if not args.confirm:
                print("❌ 清理操作需要确认，请添加 --confirm 参数")
                return 1
            
            result = cleaner.cleanup_test_files(
                args.project_id, 
                max_age_hours=args.max_age,
                confirm=True
            )
            
            if not args.quiet:
                if result['status'] == 'success':
                    print(f"\n✅ 清理完成:")
                    print(f"   删除目录数量: {result['total_deleted']}")
                    print(f"   释放空间: {result['total_size_freed_formatted']}")
                elif result['status'] == 'partial_success':
                    print(f"\n⚠️ 部分清理完成:")
                    print(f"   删除目录数量: {result['total_deleted']}")
                    print(f"   释放空间: {result['total_size_freed_formatted']}")
                    print(f"   失败数量: {len(result['failed_deletions'])}")
                elif result['status'] == 'no_files':
                    print("\n📋 没有找到需要清理的测试文件")
                else:
                    print(f"\n❌ 清理操作失败")
                
                if result.get('errors'):
                    print(f"\n⚠️ 错误信息:")
                    for error in result['errors'][:5]:  # 显示前5个错误
                        print(f"   - {error}")
        
        elif args.cleanup_images:
            # 清理指定图像
            if not args.confirm:
                print("❌ 清理操作需要确认，请添加 --confirm 参数")
                return 1
            
            result = cleaner.cleanup_specific_images(
                args.project_id,
                args.cleanup_images,
                confirm=True
            )
            
            if not args.quiet:
                if result['status'] == 'success':
                    print(f"\n✅ 指定图像清理完成:")
                    print(f"   删除目录数量: {result['total_deleted']}/{len(args.cleanup_images)}")
                    print(f"   释放空间: {result['total_size_freed_formatted']}")
                elif result['status'] == 'partial_success':
                    print(f"\n⚠️ 部分图像清理完成:")
                    print(f"   删除目录数量: {result['total_deleted']}/{len(args.cleanup_images)}")
                    print(f"   释放空间: {result['total_size_freed_formatted']}")
                    print(f"   未找到: {len(result['not_found'])}")
                    print(f"   失败数量: {len(result['failed_deletions'])}")
                else:
                    print(f"\n❌ 图像清理操作失败")
                
                if result.get('not_found'):
                    print(f"\n📋 未找到的图像:")
                    for image_name in result['not_found']:
                        print(f"   - {image_name}")
        
        elif args.scan:
            # 扫描文件
            result = cleaner.scan_test_files(args.project_id, args.max_age)
            
            if not args.quiet:
                if result['total_count'] > 0:
                    print(f"\n📋 扫描结果:")
                    print(f"   测试目录数量: {result['total_count']}")
                    print(f"   总占用空间: {cleaner._format_size(result['total_size'])}")
                    print(f"\n📁 测试目录列表:")
                    
                    for i, dir_info in enumerate(result['test_directories'][:10]):
                        age_str = f"{dir_info['age_hours']:.1f}小时前"
                        print(f"   {i+1:2d}. {dir_info['name']} ({dir_info['size_formatted']}, {age_str})")
                    
                    if len(result['test_directories']) > 10:
                        print(f"   ... 还有 {len(result['test_directories']) - 10} 个目录")
                else:
                    print("\n📋 没有找到测试文件")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        return 130
    except Exception as e:
        print(f"\n❌ 操作异常: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
