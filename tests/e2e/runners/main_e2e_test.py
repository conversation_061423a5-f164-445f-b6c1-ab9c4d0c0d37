#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端测试主入口

整合所有验证组件，提供清晰的测试报告和日志输出，确保测试环境的隔离性和可重复性
"""

import time
import json
import logging
import argparse
import sys
import os
from typing import Dict, List, Optional, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from tests.e2e.config import E2ETestConfig
from tests.e2e.core.senders import ImageTaskSender
from tests.e2e.core.validators import CeleryValidator, RedisValidator, NFSValidator


class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self, config: E2ETestConfig):
        """
        初始化测试运行器
        
        Args:
            config: 测试配置对象
        """
        self.config = config
        self.logger = self._setup_logger()
        
        # 初始化验证器
        self.celery_validator = CeleryValidator(config)
        self.redis_validator = RedisValidator(config)
        self.nfs_validator = NFSValidator(config)
        self.task_sender = ImageTaskSender(config)
        
        # 测试结果
        self.test_results = {
            'start_time': None,
            'end_time': None,
            'duration': 0,
            'overall_status': 'unknown',
            'tests': {},
            'summary': {}
        }
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("E2ETestRunner")
        logger.setLevel(getattr(logging, self.config.test_config['log_level']))
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = f"tests/e2e/logs/e2e_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        try:
            file_handler = logging.FileHandler(log_file)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            logger.info(f"📋 测试日志将保存到: {log_file}")
        except Exception as e:
            logger.warning(f"⚠️ 无法创建日志文件: {e}")
        
        return logger
    
    def run_system_validation(self) -> Dict[str, Any]:
        """
        运行系统验证测试
        
        Returns:
            Dict: 系统验证结果
        """
        self.logger.info("🎯 开始系统验证测试...")
        
        validation_results = {
            'status': 'unknown',
            'components': {},
            'summary': {}
        }
        
        # 1. Celery系统验证
        self.logger.info("📋 验证Celery系统...")
        celery_result = self.celery_validator.validate_celery_system()
        validation_results['components']['celery'] = celery_result
        
        # 2. Redis系统验证
        self.logger.info("📋 验证Redis系统...")
        redis_result = self.redis_validator.validate_redis_system()
        validation_results['components']['redis'] = redis_result
        
        # 3. NFS系统验证
        self.logger.info("📋 验证NFS系统...")
        nfs_result = self.nfs_validator.validate_nfs_system()
        validation_results['components']['nfs'] = nfs_result
        
        # 生成综合状态
        component_statuses = [
            celery_result.get('overall_status'),
            redis_result.get('overall_status'),
            nfs_result.get('overall_status')
        ]
        
        if all(status == 'healthy' for status in component_statuses):
            validation_results['status'] = 'healthy'
            self.logger.info("✅ 系统验证通过 - 所有组件健康")
        elif any(status == 'error' for status in component_statuses):
            validation_results['status'] = 'error'
            self.logger.error("❌ 系统验证失败 - 存在异常组件")
        else:
            validation_results['status'] = 'warning'
            self.logger.warning("⚠️ 系统验证部分通过 - 存在问题组件")
        
        # 生成摘要
        validation_results['summary'] = {
            'celery_status': celery_result.get('overall_status'),
            'redis_status': redis_result.get('overall_status'),
            'nfs_status': nfs_result.get('overall_status'),
            'celery_workers': celery_result.get('summary', {}).get('worker_count', 0),
            'redis_connection': redis_result.get('summary', {}).get('connection_status'),
            'nfs_accessible': nfs_result.get('summary', {}).get('is_accessible', False)
        }
        
        return validation_results

    def run_task_processing_test(self, image_type_id: int = 3,
                               project_id: str = "17") -> Dict[str, Any]:
        """
        运行任务处理测试

        Args:
            image_type_id: 图像类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道)
            project_id: 项目ID

        Returns:
            Dict: 任务处理测试结果
        """
        self.logger.info(f"🎯 开始任务处理测试 (类型={image_type_id}, 项目={project_id})...")

        test_result = {
            'status': 'unknown',
            'task_info': {},
            'processing_stages': {},
            'final_result': {}
        }

        try:
            # 1. 发送测试任务
            self.logger.info("📤 发送测试任务...")

            task_data = self.task_sender.create_task_data(
                image_type_id=image_type_id,
                project_id=project_id,
                metadata={
                    'e2e_test': True,
                    'test_timestamp': datetime.now().isoformat()
                }
            )

            send_success = self.task_sender.send_task(task_data)

            if not send_success:
                test_result['status'] = 'send_failed'
                test_result['error'] = '任务发送失败'
                return test_result

            task_id = task_data['taskId']
            image_name = task_data['imageName']

            test_result['task_info'] = {
                'task_id': task_id,
                'image_name': image_name,
                'image_type_id': image_type_id,
                'project_id': project_id,
                'send_time': datetime.now().isoformat()
            }

            self.logger.info(f"✅ 任务发送成功: {task_id}")

            # 2. 等待一段时间让任务开始处理
            self.logger.info("⏳ 等待任务开始处理...")
            time.sleep(5)  # 等待5秒让任务进入处理队列

            # 3. 监控NFS文件生成（主要验证方式）
            self.logger.info("🔍 监控NFS文件生成...")

            nfs_monitor_result = self.nfs_validator.monitor_file_generation(
                project_id, image_name, image_type_id,
                timeout=self.config.test_config['test_timeout']
            )
            test_result['processing_stages']['nfs_monitoring'] = nfs_monitor_result

            # 4. 检查Redis中的任务存储情况（可选）
            self.logger.info("🔍 检查Redis任务存储...")

            celery_storage_result = self.redis_validator.check_celery_task_storage()
            test_result['processing_stages']['redis_storage'] = celery_storage_result

            # 5. 生成最终结果
            nfs_success = nfs_monitor_result.get('final_complete', False)
            task_found_in_redis = celery_storage_result.get('task_count', 0) > 0

            if nfs_success:
                test_result['status'] = 'success'
                self.logger.info("✅ 任务处理测试成功 - NFS文件已生成")
            elif task_found_in_redis:
                test_result['status'] = 'partial_success'
                self.logger.warning("⚠️ 任务处理测试部分成功 - 任务已处理但文件可能未完成")
            else:
                test_result['status'] = 'failed'
                self.logger.error("❌ 任务处理测试失败 - 无法验证任务处理")

            test_result['final_result'] = {
                'nfs_files_generated': nfs_success,
                'task_found_in_redis': task_found_in_redis,
                'nfs_monitoring_time': nfs_monitor_result.get('monitoring_time', 0),
                'redis_task_count': celery_storage_result.get('task_count', 0),
                'redis_task_states': celery_storage_result.get('task_states', {})
            }

        except Exception as e:
            test_result['status'] = 'error'
            test_result['error'] = str(e)
            self.logger.error(f"❌ 任务处理测试异常: {e}")

        return test_result

    def run_batch_processing_test(self, task_count: int = 3,
                                image_type_id: int = 3) -> Dict[str, Any]:
        """
        运行批量处理测试

        Args:
            task_count: 任务数量
            image_type_id: 图像类型ID

        Returns:
            Dict: 批量处理测试结果
        """
        self.logger.info(f"🎯 开始批量处理测试 (数量={task_count}, 类型={image_type_id})...")

        test_result = {
            'status': 'unknown',
            'task_count': task_count,
            'tasks': [],
            'summary': {}
        }

        try:
            # 发送批量任务
            self.logger.info(f"📤 发送 {task_count} 个批量任务...")

            send_results = self.task_sender.send_multiple_tasks(
                task_count,
                image_type_id=image_type_id,
                project_id="17"
            )

            successful_sends = sum(send_results)

            if successful_sends == 0:
                test_result['status'] = 'send_failed'
                test_result['error'] = '所有任务发送失败'
                return test_result

            self.logger.info(f"✅ 成功发送 {successful_sends}/{task_count} 个任务")

            # 等待一段时间让任务处理
            wait_time = min(60, task_count * 20)  # 每个任务最多等待20秒
            self.logger.info(f"⏳ 等待 {wait_time} 秒让任务处理...")
            time.sleep(wait_time)

            # 检查Celery任务存储状态
            celery_storage_result = self.redis_validator.check_celery_task_storage()

            test_result['summary'] = {
                'tasks_sent': successful_sends,
                'send_success_rate': successful_sends / task_count,
                'celery_task_count': celery_storage_result.get('task_count', 0),
                'celery_task_states': celery_storage_result.get('task_states', {})
            }

            if successful_sends == task_count:
                test_result['status'] = 'success'
                self.logger.info("✅ 批量处理测试成功")
            else:
                test_result['status'] = 'partial_success'
                self.logger.warning("⚠️ 批量处理测试部分成功")

        except Exception as e:
            test_result['status'] = 'error'
            test_result['error'] = str(e)
            self.logger.error(f"❌ 批量处理测试异常: {e}")

        return test_result

    def run_full_e2e_test(self, include_batch: bool = True) -> Dict[str, Any]:
        """
        运行完整的端到端测试

        Args:
            include_batch: 是否包含批量测试

        Returns:
            Dict: 完整测试结果
        """
        self.logger.info("🚀 开始完整端到端测试...")

        self.test_results['start_time'] = datetime.now().isoformat()
        start_time = time.time()

        try:
            # 1. 系统验证
            self.logger.info("=" * 60)
            self.logger.info("第一阶段: 系统验证")
            self.logger.info("=" * 60)

            system_validation = self.run_system_validation()
            self.test_results['tests']['system_validation'] = system_validation

            # 如果系统验证失败，跳过后续测试
            if system_validation['status'] == 'error':
                self.logger.error("❌ 系统验证失败，跳过后续测试")
                self.test_results['overall_status'] = 'system_error'
                return self.test_results

            # 2. 单任务处理测试
            self.logger.info("=" * 60)
            self.logger.info("第二阶段: 单任务处理测试")
            self.logger.info("=" * 60)

            single_task_test = self.run_task_processing_test()
            self.test_results['tests']['single_task'] = single_task_test

            # 3. 批量任务测试（可选）
            if include_batch:
                self.logger.info("=" * 60)
                self.logger.info("第三阶段: 批量任务处理测试")
                self.logger.info("=" * 60)

                batch_test = self.run_batch_processing_test()
                self.test_results['tests']['batch_tasks'] = batch_test

            # 4. 生成最终结果
            self._generate_final_results()

        except Exception as e:
            self.test_results['overall_status'] = 'error'
            self.test_results['error'] = str(e)
            self.logger.error(f"❌ 端到端测试异常: {e}")
        finally:
            self.test_results['end_time'] = datetime.now().isoformat()
            self.test_results['duration'] = time.time() - start_time

            # 生成测试报告
            self._generate_test_report()

        return self.test_results

    def _generate_final_results(self):
        """生成最终测试结果"""
        tests = self.test_results['tests']

        # 统计各阶段状态
        system_status = tests.get('system_validation', {}).get('status', 'unknown')
        single_task_status = tests.get('single_task', {}).get('status', 'unknown')
        batch_status = tests.get('batch_tasks', {}).get('status', 'unknown')

        # 生成综合状态
        if system_status == 'healthy':
            if single_task_status == 'success':
                if 'batch_tasks' not in tests or batch_status in ['success', 'partial_success']:
                    self.test_results['overall_status'] = 'success'
                else:
                    self.test_results['overall_status'] = 'partial_success'
            elif single_task_status in ['partial_success', 'failed']:
                self.test_results['overall_status'] = 'partial_success'
            else:
                self.test_results['overall_status'] = 'failed'
        else:
            self.test_results['overall_status'] = 'system_issues'

        # 生成摘要
        self.test_results['summary'] = {
            'system_validation': system_status,
            'single_task_processing': single_task_status,
            'batch_task_processing': batch_status if 'batch_tasks' in tests else 'skipped',
            'total_duration': self.test_results['duration'],
            'test_environment': self.config.env
        }

    def _generate_test_report(self):
        """生成测试报告"""
        self.logger.info("=" * 80)
        self.logger.info("🎉 端到端测试完成 - 测试报告")
        self.logger.info("=" * 80)

        # 基本信息
        self.logger.info(f"📋 测试环境: {self.config.env}")
        self.logger.info(f"📋 开始时间: {self.test_results['start_time']}")
        self.logger.info(f"📋 结束时间: {self.test_results['end_time']}")
        self.logger.info(f"📋 总耗时: {self.test_results['duration']:.2f} 秒")
        self.logger.info(f"📋 综合状态: {self.test_results['overall_status']}")

        # 各阶段结果
        self.logger.info("\n📊 测试阶段结果:")

        tests = self.test_results['tests']

        # 系统验证
        if 'system_validation' in tests:
            system_result = tests['system_validation']
            self.logger.info(f"   🔧 系统验证: {system_result['status']}")

            summary = system_result.get('summary', {})
            self.logger.info(f"      - Celery: {summary.get('celery_status')} ({summary.get('celery_workers', 0)} workers)")
            self.logger.info(f"      - Redis: {summary.get('redis_status')}")
            self.logger.info(f"      - NFS: {summary.get('nfs_status')} ({'可访问' if summary.get('nfs_accessible') else '不可访问'})")

        # 单任务测试
        if 'single_task' in tests:
            single_result = tests['single_task']
            self.logger.info(f"   📤 单任务处理: {single_result['status']}")

            if 'final_result' in single_result:
                final = single_result['final_result']
                self.logger.info(f"      - Redis任务完成: {'是' if final.get('redis_task_completed') else '否'}")
                self.logger.info(f"      - NFS文件生成: {'是' if final.get('nfs_files_generated') else '否'}")
                self.logger.info(f"      - 任务最终状态: {final.get('redis_final_state', 'N/A')}")

        # 批量任务测试
        if 'batch_tasks' in tests:
            batch_result = tests['batch_tasks']
            self.logger.info(f"   📦 批量任务处理: {batch_result['status']}")

            if 'summary' in batch_result:
                summary = batch_result['summary']
                self.logger.info(f"      - 任务发送成功率: {summary.get('send_success_rate', 0):.1%}")
                self.logger.info(f"      - Celery任务数量: {summary.get('celery_task_count', 0)}")

        # 建议和下一步
        self.logger.info("\n💡 建议和下一步:")

        overall_status = self.test_results['overall_status']

        if overall_status == 'success':
            self.logger.info("   ✅ 系统运行正常，可以进行生产部署")
        elif overall_status == 'partial_success':
            self.logger.info("   ⚠️ 系统部分功能正常，建议检查以下问题:")
            self.logger.info("      - 检查失败的测试阶段")
            self.logger.info("      - 查看详细日志了解具体问题")
            self.logger.info("      - 修复问题后重新运行测试")
        elif overall_status == 'system_issues':
            self.logger.info("   ❌ 系统存在基础设施问题，建议:")
            self.logger.info("      - 检查Celery worker是否正常运行")
            self.logger.info("      - 检查Redis连接和配置")
            self.logger.info("      - 检查NFS存储挂载和权限")
        else:
            self.logger.info("   ❌ 测试失败，建议:")
            self.logger.info("      - 查看详细错误日志")
            self.logger.info("      - 检查系统配置")
            self.logger.info("      - 联系系统管理员")

        self.logger.info("=" * 80)

        # 保存JSON报告
        self._save_json_report()

    def _save_json_report(self):
        """保存JSON格式的测试报告"""
        try:
            report_file = f"tests/e2e/reports/e2e_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)

            self.logger.info(f"📋 JSON测试报告已保存: {report_file}")

        except Exception as e:
            self.logger.warning(f"⚠️ 保存JSON报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='端到端测试工具')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev',
                       help='测试环境 (默认: dev)')
    parser.add_argument('--no-batch', action='store_true',
                       help='跳过批量任务测试')
    parser.add_argument('--image-type', type=int, choices=[1, 2, 3, 4], default=3,
                       help='图像类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道, 默认: 3)')
    parser.add_argument('--project-id', default='17',
                       help='项目ID (默认: 17)')
    parser.add_argument('--batch-count', type=int, default=3,
                       help='批量任务数量 (默认: 3)')

    args = parser.parse_args()

    # 创建配置和测试运行器
    config = E2ETestConfig(args.env)
    runner = E2ETestRunner(config)

    print("🎯 AiLabel端到端测试工具")
    print("=" * 50)
    print(f"测试环境: {args.env}")
    print(f"图像类型: {args.image_type}")
    print(f"项目ID: {args.project_id}")
    print(f"批量测试: {'否' if args.no_batch else f'是 ({args.batch_count}个任务)'}")
    print("=" * 50)

    # 运行测试
    if args.no_batch:
        # 只运行系统验证和单任务测试
        results = runner.run_full_e2e_test(include_batch=False)
    else:
        # 运行完整测试
        results = runner.run_full_e2e_test(include_batch=True)

    # 返回适当的退出码
    if results['overall_status'] == 'success':
        exit(0)
    elif results['overall_status'] in ['partial_success', 'system_issues']:
        exit(1)
    else:
        exit(2)


if __name__ == "__main__":
    main()
