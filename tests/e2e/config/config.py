#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端测试配置文件

提供测试环境的配置管理，支持不同环境的配置加载
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

class E2ETestConfig:
    """端到端测试配置类"""
    
    def __init__(self, env='dev'):
        """
        初始化测试配置
        
        Args:
            env (str): 环境类型，'dev' 或 'prod'
        """
        self.env = env
        self.project_root = project_root
        self._load_env_config()
        
    def _load_env_config(self):
        """加载环境配置"""
        env_file = f'.env.{self.env}'
        env_path = os.path.join(self.project_root, env_file)
        
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"✅ 已加载测试环境配置: {env_file}")
        else:
            print(f"⚠️ 环境配置文件不存在: {env_file}")
            
    @property
    def rabbitmq_config(self):
        """RabbitMQ配置"""
        return {
            'host': os.getenv('RABBITMQ_HOST', '**************'),
            'port': int(os.getenv('RABBITMQ_PORT', '25675')),
            'username': os.getenv('RABBITMQ_ACCOUNT', 'admin'),
            'password': os.getenv('RABBITMQ_PASSWORD', 'vipa@404'),
            'virtual_host': '/',
            'heartbeat': int(os.getenv('RABBITMQ_HEARTBEAT', '300')),
            'connection_timeout': int(os.getenv('RABBITMQ_CONNECTION_TIMEOUT', '60'))
        }
    
    @property
    def redis_config(self):
        """Redis配置"""
        return {
            'host': os.getenv('REDIS_HOST', '**************'),
            'port': int(os.getenv('REDIS_PORT', '26379')),
            'db': 0
        }
    
    @property
    def mysql_config(self):
        """MySQL配置"""
        return {
            'host': os.getenv('MYSQL_HOST', '**************'),
            'port': int(os.getenv('MYSQL_PORT', '23306')),
            'username': os.getenv('MYSQL_USERNAME', 'admin'),
            'password': os.getenv('MYSQL_PASSWORD', 'vipa@404_admin'),
            'database': os.getenv('MYSQL_DB', 'medlabel')
        }
    
    @property
    def nfs_config(self):
        """NFS配置"""
        return {
            'project_save_dir': os.getenv('PROJECT_SAVE_DIR', '/nfs5/medlabel/medlabel_212/projects')
        }
    
    @property
    def queue_config(self):
        """队列配置"""
        return {
            'image_convert_queue': 'medlabel_image_convert_queue',
            'ailabel_queue': 'ailabel_queue'
        }
    
    @property
    def test_config(self):
        """测试专用配置"""
        return {
            'test_timeout': 300,  # 测试超时时间（秒）
            'retry_attempts': 3,  # 重试次数
            'retry_delay': 5,     # 重试间隔（秒）
            'cleanup_after_test': True,  # 测试后是否清理
            'log_level': 'INFO'
        }

# 默认配置实例
default_config = E2ETestConfig()
