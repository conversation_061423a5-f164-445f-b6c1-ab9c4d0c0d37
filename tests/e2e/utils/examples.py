#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端测试使用示例

展示如何使用各个测试组件
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from test.tests.e2e.config import E2ETestConfig
from test.tests.e2e.task_sender import ImageTaskSender, send_image_task
from test.tests.e2e.celery_validator import validate_celery
from test.tests.e2e.redis_validator import validate_redis
from test.tests.e2e.nfs_validator import validate_nfs, check_image_files


def example_system_validation():
    """示例：系统验证"""
    print("🎯 示例：系统验证")
    print("=" * 40)
    
    # 使用便捷函数进行快速验证
    print("📋 验证Celery系统...")
    celery_result = validate_celery()
    print(f"   结果: {celery_result['overall_status']}")
    
    print("📋 验证Redis系统...")
    redis_result = validate_redis()
    print(f"   结果: {redis_result['overall_status']}")
    
    print("📋 验证NFS系统...")
    nfs_result = validate_nfs()
    print(f"   结果: {nfs_result['overall_status']}")
    
    return all(result['overall_status'] == 'healthy' 
              for result in [celery_result, redis_result, nfs_result])


def example_task_sending():
    """示例：任务发送"""
    print("\n🎯 示例：任务发送")
    print("=" * 40)
    
    # 方式一：使用便捷函数
    print("📤 发送单个病理图任务...")
    success = send_image_task(
        image_type_id=3,
        project_id="17",
        metadata={'example': True}
    )
    print(f"   结果: {'成功' if success else '失败'}")
    
    # 方式二：使用任务发送器类
    print("📤 发送多个任务...")
    config = E2ETestConfig('dev')
    sender = ImageTaskSender(config)
    
    results = sender.send_multiple_tasks(
        count=2,
        image_type_id=1,  # 普通图像
        project_id="17"
    )
    
    success_count = sum(results)
    print(f"   结果: {success_count}/{len(results)} 成功")
    
    return success


def example_file_checking():
    """示例：文件检查"""
    print("\n🎯 示例：文件检查")
    print("=" * 40)
    
    # 检查不同类型的图像文件
    test_cases = [
        ("17", "test_image_1", 1, "普通图像"),
        ("17", "test_image_2", 2, "DICOM图像"),
        ("17", "test_pathology", 3, "病理图像"),
        ("17", "test_multichannel", 4, "多通道图像")
    ]
    
    for project_id, image_name, image_type, description in test_cases:
        print(f"📋 检查{description}: {image_name}")
        
        result = check_image_files(project_id, image_name, image_type)
        
        print(f"   状态: {result['status']}")
        print(f"   完整: {'是' if result.get('complete', False) else '否'}")


def example_custom_validation():
    """示例：自定义验证流程"""
    print("\n🎯 示例：自定义验证流程")
    print("=" * 40)
    
    # 创建配置
    config = E2ETestConfig('dev')
    
    # 1. 先验证系统基础设施
    from test.tests.e2e.celery_validator import CeleryValidator
    from test.tests.e2e.redis_validator import RedisValidator
    
    celery_validator = CeleryValidator(config)
    redis_validator = RedisValidator(config)
    
    print("📋 检查Celery进程...")
    process_result = celery_validator.check_celery_processes()
    print(f"   发现 {process_result['worker_count']} 个worker进程")
    
    print("📋 检查Redis连接...")
    connection_result = redis_validator.check_redis_connection()
    print(f"   连接状态: {connection_result['status']}")
    
    if connection_result['status'] == 'connected':
        print(f"   连接耗时: {connection_result['connection_time']:.3f}秒")
    
    # 2. 发送测试任务并监控
    if process_result['worker_count'] > 0 and connection_result['status'] == 'connected':
        print("📤 发送测试任务...")
        
        sender = ImageTaskSender(config)
        task_data = sender.create_task_data(
            image_type_id=3,
            project_id="17",
            metadata={'custom_test': True}
        )
        
        if sender.send_task(task_data):
            task_id = task_data['taskId']
            print(f"   任务ID: {task_id}")
            
            # 监控任务状态
            print("🔍 监控任务状态...")
            lifecycle_result = redis_validator.monitor_task_lifecycle(task_id, timeout=30)
            
            print(f"   监控结果: {lifecycle_result['status']}")
            print(f"   最终状态: {lifecycle_result.get('final_state', 'N/A')}")
            
            if lifecycle_result['lifecycle']:
                print("   状态变化:")
                for entry in lifecycle_result['lifecycle']:
                    print(f"     {entry['timestamp']:.1f}s: {entry['state']}")


def example_batch_testing():
    """示例：批量测试"""
    print("\n🎯 示例：批量测试")
    print("=" * 40)
    
    config = E2ETestConfig('dev')
    
    # 发送多种类型的任务
    sender = ImageTaskSender(config)
    
    task_types = [
        (1, "普通图像"),
        (2, "DICOM图像"),
        (3, "病理图像")
    ]
    
    all_results = []
    
    for image_type, description in task_types:
        print(f"📤 发送{description}任务...")
        
        results = sender.send_multiple_tasks(
            count=2,
            image_type_id=image_type,
            project_id="17"
        )
        
        success_count = sum(results)
        print(f"   {description}: {success_count}/{len(results)} 成功")
        
        all_results.extend(results)
    
    total_success = sum(all_results)
    print(f"\n📊 总体结果: {total_success}/{len(all_results)} 成功")
    print(f"   成功率: {total_success/len(all_results):.1%}")


def main():
    """主函数 - 运行所有示例"""
    print("🎯 AiLabel端到端测试组件使用示例")
    print("=" * 60)
    
    try:
        # 1. 系统验证示例
        system_ok = example_system_validation()
        
        if not system_ok:
            print("\n⚠️ 系统验证失败，跳过后续示例")
            return
        
        # 2. 任务发送示例
        example_task_sending()
        
        # 3. 文件检查示例
        example_file_checking()
        
        # 4. 自定义验证示例
        example_custom_validation()
        
        # 5. 批量测试示例
        example_batch_testing()
        
        print("\n✅ 所有示例运行完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
