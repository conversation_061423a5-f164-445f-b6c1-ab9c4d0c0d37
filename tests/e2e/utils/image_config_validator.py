#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像配置验证工具

验证任务发送器中配置的图像文件路径是否存在，并提供配置建议
"""

import os
import sys
from typing import Dict, List, Any, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from tests.e2e.core.senders import ImageTaskSender, get_supported_image_types


class ImageConfigValidator:
    """图像配置验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.sender = ImageTaskSender()
        self.supported_types = get_supported_image_types()
    
    def validate_single_url(self, url: str) -> Dict[str, Any]:
        """
        验证单个URL
        
        Args:
            url: 图像文件URL/路径
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'url': url,
            'exists': False,
            'is_file': False,
            'is_readable': False,
            'size': 0,
            'extension': '',
            'status': 'unknown'
        }
        
        try:
            path = Path(url)
            result['extension'] = path.suffix.lower()
            
            if path.exists():
                result['exists'] = True
                
                if path.is_file():
                    result['is_file'] = True
                    result['is_readable'] = os.access(str(path), os.R_OK)
                    result['size'] = path.stat().st_size
                    
                    if result['is_readable']:
                        result['status'] = 'valid'
                    else:
                        result['status'] = 'permission_denied'
                else:
                    result['status'] = 'not_a_file'
            else:
                result['status'] = 'not_found'
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        
        return result
    
    def validate_image_type_urls(self, image_type_id: int) -> Dict[str, Any]:
        """
        验证特定图像类型的所有URL
        
        Args:
            image_type_id: 图像类型ID
            
        Returns:
            Dict: 验证结果
        """
        if image_type_id not in self.supported_types:
            return {
                'image_type_id': image_type_id,
                'status': 'unsupported',
                'error': f'不支持的图像类型ID: {image_type_id}'
            }
        
        config = self.supported_types[image_type_id]
        urls = config['default_urls']
        
        result = {
            'image_type_id': image_type_id,
            'image_type_name': config['name'],
            'total_urls': len(urls),
            'url_results': [],
            'summary': {
                'valid_count': 0,
                'invalid_count': 0,
                'error_count': 0
            }
        }
        
        for i, url in enumerate(urls):
            url_result = self.validate_single_url(url)
            url_result['index'] = i
            result['url_results'].append(url_result)
            
            # 更新统计
            if url_result['status'] == 'valid':
                result['summary']['valid_count'] += 1
            elif url_result['status'] == 'error':
                result['summary']['error_count'] += 1
            else:
                result['summary']['invalid_count'] += 1
        
        # 确定整体状态
        if result['summary']['valid_count'] > 0:
            result['status'] = 'partial_valid' if result['summary']['invalid_count'] > 0 else 'all_valid'
        else:
            result['status'] = 'all_invalid'
        
        return result
    
    def validate_all_image_types(self) -> Dict[str, Any]:
        """
        验证所有图像类型的URL配置
        
        Returns:
            Dict: 完整的验证结果
        """
        result = {
            'timestamp': str(Path().cwd()),
            'total_types': len(self.supported_types),
            'type_results': {},
            'overall_summary': {
                'types_with_valid_urls': 0,
                'types_all_invalid': 0,
                'total_urls_checked': 0,
                'total_valid_urls': 0,
                'total_invalid_urls': 0
            }
        }
        
        for image_type_id in self.supported_types.keys():
            type_result = self.validate_image_type_urls(image_type_id)
            result['type_results'][image_type_id] = type_result
            
            # 更新总体统计
            if type_result['status'] in ['all_valid', 'partial_valid']:
                result['overall_summary']['types_with_valid_urls'] += 1
            elif type_result['status'] == 'all_invalid':
                result['overall_summary']['types_all_invalid'] += 1
            
            result['overall_summary']['total_urls_checked'] += type_result['total_urls']
            result['overall_summary']['total_valid_urls'] += type_result['summary']['valid_count']
            result['overall_summary']['total_invalid_urls'] += type_result['summary']['invalid_count']
        
        return result
    
    def generate_config_suggestions(self, validation_result: Dict[str, Any]) -> List[str]:
        """
        根据验证结果生成配置建议
        
        Args:
            validation_result: validate_all_image_types的结果
            
        Returns:
            List[str]: 配置建议列表
        """
        suggestions = []
        
        for image_type_id, type_result in validation_result['type_results'].items():
            type_name = type_result['image_type_name']
            
            if type_result['status'] == 'all_invalid':
                suggestions.append(f"❌ {type_name}(类型{image_type_id}): 所有默认URL都无效，需要更新配置")
                
                # 建议替换URL
                config = self.supported_types[image_type_id]
                extensions = config['extensions']
                suggestions.append(f"   建议: 请提供有效的{', '.join(extensions)}格式文件路径")
                
            elif type_result['status'] == 'partial_valid':
                valid_count = type_result['summary']['valid_count']
                total_count = type_result['total_urls']
                suggestions.append(f"⚠️ {type_name}(类型{image_type_id}): {valid_count}/{total_count} 个URL有效")
                
                # 列出无效的URL
                for url_result in type_result['url_results']:
                    if url_result['status'] != 'valid':
                        suggestions.append(f"   无效URL[{url_result['index']}]: {url_result['url']} ({url_result['status']})")
            
            else:  # all_valid
                suggestions.append(f"✅ {type_name}(类型{image_type_id}): 所有URL都有效")
        
        # 总体建议
        overall = validation_result['overall_summary']
        if overall['types_all_invalid'] > 0:
            suggestions.append(f"\n🔧 建议: 有 {overall['types_all_invalid']} 种图像类型需要更新URL配置")
            suggestions.append("   可以修改 tests/e2e/core/senders/task_sender.py 中的 IMAGE_TYPE_CONFIGS")
        
        if overall['total_valid_urls'] == 0:
            suggestions.append("\n⚠️ 警告: 没有找到任何有效的图像文件，测试可能无法正常运行")
            suggestions.append("   建议: 请确保测试环境中有可访问的示例图像文件")
        
        return suggestions
    
    def print_validation_report(self, validation_result: Dict[str, Any]):
        """
        打印验证报告
        
        Args:
            validation_result: validate_all_image_types的结果
        """
        print("🔍 图像配置验证报告")
        print("=" * 60)
        
        # 总体统计
        overall = validation_result['overall_summary']
        print(f"📊 总体统计:")
        print(f"   图像类型数量: {validation_result['total_types']}")
        print(f"   检查的URL总数: {overall['total_urls_checked']}")
        print(f"   有效URL数量: {overall['total_valid_urls']}")
        print(f"   无效URL数量: {overall['total_invalid_urls']}")
        print(f"   有效类型数量: {overall['types_with_valid_urls']}")
        
        print("\n📋 详细结果:")
        
        # 各类型详细结果
        for image_type_id, type_result in validation_result['type_results'].items():
            status_icon = {
                'all_valid': '✅',
                'partial_valid': '⚠️',
                'all_invalid': '❌',
                'unsupported': '🚫'
            }.get(type_result['status'], '❓')
            
            print(f"\n{status_icon} {type_result['image_type_name']} (类型 {image_type_id}):")
            print(f"   状态: {type_result['status']}")
            print(f"   有效URL: {type_result['summary']['valid_count']}/{type_result['total_urls']}")
            
            # 显示URL详情
            for url_result in type_result['url_results']:
                status_symbol = '✅' if url_result['status'] == 'valid' else '❌'
                size_info = f" ({url_result['size']} bytes)" if url_result['size'] > 0 else ""
                print(f"     {status_symbol} URL[{url_result['index']}]: {url_result['url']}{size_info}")
                if url_result['status'] != 'valid':
                    print(f"         原因: {url_result['status']}")
        
        # 配置建议
        print(f"\n💡 配置建议:")
        suggestions = self.generate_config_suggestions(validation_result)
        for suggestion in suggestions:
            print(f"   {suggestion}")


def main():
    """主函数"""
    print("🎯 图像配置验证工具")
    print("=" * 60)
    
    try:
        validator = ImageConfigValidator()
        
        # 执行验证
        print("🔍 正在验证图像配置...")
        validation_result = validator.validate_all_image_types()
        
        # 打印报告
        validator.print_validation_report(validation_result)
        
        print(f"\n✅ 验证完成!")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
