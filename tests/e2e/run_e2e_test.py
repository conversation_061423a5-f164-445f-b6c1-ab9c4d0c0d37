#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端测试便捷运行脚本

提供简单的接口来运行各种端到端测试
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from tests.e2e.runners import E2ETestRunner
from tests.e2e.config import E2ETestConfig


def run_quick_validation(env='dev'):
    """
    快速系统验证
    
    Args:
        env: 环境类型 ('dev' 或 'prod')
    """
    print("🎯 快速系统验证")
    print("=" * 40)
    
    config = E2ETestConfig(env)
    runner = E2ETestRunner(config)
    
    result = runner.run_system_validation()
    
    print(f"\n📊 验证结果: {result['status']}")
    
    summary = result.get('summary', {})
    print(f"   Celery: {summary.get('celery_status')} ({summary.get('celery_workers', 0)} workers)")
    print(f"   Redis: {summary.get('redis_status')}")
    print(f"   NFS: {summary.get('nfs_status')}")
    
    return result['status'] == 'healthy'


def run_single_task_test(env='dev', image_type=3, project_id='17'):
    """
    单任务测试
    
    Args:
        env: 环境类型
        image_type: 图像类型ID
        project_id: 项目ID
    """
    print("🎯 单任务处理测试")
    print("=" * 40)
    
    config = E2ETestConfig(env)
    runner = E2ETestRunner(config)
    
    result = runner.run_task_processing_test(image_type, project_id)
    
    print(f"\n📊 测试结果: {result['status']}")
    
    if 'task_info' in result:
        task_info = result['task_info']
        print(f"   任务ID: {task_info['task_id']}")
        print(f"   图像名称: {task_info['image_name']}")
    
    if 'final_result' in result:
        final = result['final_result']
        print(f"   Redis任务完成: {'是' if final.get('redis_task_completed') else '否'}")
        print(f"   NFS文件生成: {'是' if final.get('nfs_files_generated') else '否'}")
    
    return result['status'] == 'success'


def run_batch_test(env='dev', count=3, image_type=3):
    """
    批量任务测试
    
    Args:
        env: 环境类型
        count: 任务数量
        image_type: 图像类型ID
    """
    print("🎯 批量任务处理测试")
    print("=" * 40)
    
    config = E2ETestConfig(env)
    runner = E2ETestRunner(config)
    
    result = runner.run_batch_processing_test(count, image_type)
    
    print(f"\n📊 测试结果: {result['status']}")
    
    if 'summary' in result:
        summary = result['summary']
        print(f"   发送成功率: {summary.get('send_success_rate', 0):.1%}")
        print(f"   Celery任务数: {summary.get('celery_task_count', 0)}")
    
    return result['status'] in ['success', 'partial_success']


def run_full_test(env='dev'):
    """
    完整端到端测试
    
    Args:
        env: 环境类型
    """
    print("🎯 完整端到端测试")
    print("=" * 40)
    
    config = E2ETestConfig(env)
    runner = E2ETestRunner(config)
    
    result = runner.run_full_e2e_test()
    
    return result['overall_status'] == 'success'


def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎯 AiLabel端到端测试工具")
        print("=" * 50)
        print("1. 快速系统验证")
        print("2. 单任务处理测试")
        print("3. 批量任务测试")
        print("4. 完整端到端测试")
        print("5. 退出")
        print("=" * 50)
        
        choice = input("请选择测试类型 (1-5): ").strip()
        
        if choice == '5':
            print("👋 再见!")
            break
        
        # 选择环境
        env_choice = input("选择环境 (1=dev, 2=prod, 默认=dev): ").strip()
        env = 'prod' if env_choice == '2' else 'dev'
        
        print(f"\n🎯 使用环境: {env}")
        
        try:
            if choice == '1':
                success = run_quick_validation(env)
            elif choice == '2':
                image_type = input("图像类型 (1=普通, 2=DICOM, 3=病理, 4=多通道, 默认=3): ").strip()
                image_type = int(image_type) if image_type.isdigit() and image_type in ['1', '2', '3', '4'] else 3
                
                project_id = input("项目ID (默认=17): ").strip() or '17'
                
                success = run_single_task_test(env, image_type, project_id)
            elif choice == '3':
                count = input("任务数量 (默认=3): ").strip()
                count = int(count) if count.isdigit() else 3
                
                image_type = input("图像类型 (1=普通, 2=DICOM, 3=病理, 4=多通道, 默认=3): ").strip()
                image_type = int(image_type) if image_type.isdigit() and image_type in ['1', '2', '3', '4'] else 3
                
                success = run_batch_test(env, count, image_type)
            elif choice == '4':
                success = run_full_test(env)
            else:
                print("❌ 无效选择，请重试")
                continue
            
            if success:
                print("\n✅ 测试成功!")
            else:
                print("\n❌ 测试失败，请查看详细日志")
                
        except KeyboardInterrupt:
            print("\n\n⚠️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == 'validate':
            env = sys.argv[2] if len(sys.argv) > 2 else 'dev'
            success = run_quick_validation(env)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == 'single':
            env = sys.argv[2] if len(sys.argv) > 2 else 'dev'
            success = run_single_task_test(env)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == 'batch':
            env = sys.argv[2] if len(sys.argv) > 2 else 'dev'
            success = run_batch_test(env)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == 'full':
            env = sys.argv[2] if len(sys.argv) > 2 else 'dev'
            success = run_full_test(env)
            sys.exit(0 if success else 1)
        else:
            print("用法:")
            print("  python run_e2e_test.py validate [dev|prod]  # 快速验证")
            print("  python run_e2e_test.py single [dev|prod]    # 单任务测试")
            print("  python run_e2e_test.py batch [dev|prod]     # 批量测试")
            print("  python run_e2e_test.py full [dev|prod]      # 完整测试")
            print("  python run_e2e_test.py                      # 交互式菜单")
            sys.exit(1)
    else:
        # 交互式模式
        interactive_menu()
