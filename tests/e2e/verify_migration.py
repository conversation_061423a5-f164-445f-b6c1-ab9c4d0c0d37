#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
E2E测试目录重组验证脚本

验证目录重组后所有模块是否能正常导入和工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)


def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试配置模块
        from tests.e2e.config import E2ETestConfig
        print("✅ 配置模块导入成功")
        
        # 测试验证器模块
        from tests.e2e.core.validators import CeleryValidator, RedisValidator, NFSValidator
        print("✅ 验证器模块导入成功")
        
        # 测试任务发送器模块
        from tests.e2e.core.senders import ImageTaskSender
        print("✅ 任务发送器模块导入成功")
        
        # 测试运行器模块
        from tests.e2e.runners import E2ETestRunner
        print("✅ 测试运行器模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    base_dir = os.path.dirname(__file__)
    
    # 检查必要的目录
    required_dirs = [
        'config',
        'core',
        'core/validators',
        'core/senders',
        'runners',
        'utils',
        'reports',
        'logs'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = os.path.join(base_dir, dir_name)
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_name)
        else:
            print(f"✅ 目录存在: {dir_name}")
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    
    return True


def test_files_exist():
    """测试关键文件是否存在"""
    print("\n🔍 测试关键文件...")
    
    base_dir = os.path.dirname(__file__)
    
    # 检查必要的文件
    required_files = [
        'config/__init__.py',
        'config/config.py',
        'core/__init__.py',
        'core/validators/__init__.py',
        'core/validators/celery_validator.py',
        'core/validators/redis_validator.py',
        'core/validators/nfs_validator.py',
        'core/senders/__init__.py',
        'core/senders/task_sender.py',
        'runners/__init__.py',
        'runners/main_e2e_test.py',
        'utils/__init__.py',
        'utils/examples.py',
        'run_tests.py',
        'run_e2e_test.py',
        'README.md',
        'MIGRATION.md',
        '.gitignore'
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(base_dir, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
        else:
            print(f"✅ 文件存在: {file_name}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    return True


def test_instantiation():
    """测试类实例化"""
    print("\n🔍 测试类实例化...")
    
    try:
        # 测试配置类
        from tests.e2e.config import E2ETestConfig
        config = E2ETestConfig('dev')
        print("✅ E2ETestConfig 实例化成功")
        
        # 测试验证器类
        from tests.e2e.core.validators import CeleryValidator, RedisValidator, NFSValidator
        celery_validator = CeleryValidator(config)
        redis_validator = RedisValidator(config)
        nfs_validator = NFSValidator(config)
        print("✅ 验证器类实例化成功")
        
        # 测试任务发送器类
        from tests.e2e.core.senders import ImageTaskSender
        task_sender = ImageTaskSender(config)
        print("✅ ImageTaskSender 实例化成功")
        
        # 测试运行器类
        from tests.e2e.runners import E2ETestRunner
        runner = E2ETestRunner(config)
        print("✅ E2ETestRunner 实例化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 类实例化失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 E2E测试目录重组验证")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("目录结构测试", test_directory_structure),
        ("文件存在测试", test_files_exist),
        ("类实例化测试", test_instantiation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！目录重组成功！")
        print("\n💡 下一步:")
        print("   1. 运行 python tests/e2e/run_tests.py --validate-only --env dev")
        print("   2. 检查测试报告和日志是否正确生成")
        print("   3. 更新任何自定义脚本中的导入路径")
        return True
    else:
        print("❌ 部分测试失败，请检查问题并修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
