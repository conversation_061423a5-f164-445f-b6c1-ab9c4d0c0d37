#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NFS测试文件清理组件

该组件提供以下功能：
1. 安全清理测试生成的文件和目录
2. 支持按项目、图像名称、时间范围清理
3. 提供清理预览和确认机制
4. 详细的清理日志记录
"""

import os
import shutil
import time
import glob
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

from tests.e2e.config.config import default_config


class NFSCleaner:
    """NFS测试文件清理器"""
    
    def __init__(self, config=None):
        """
        初始化NFS清理器
        
        Args:
            config: 配置对象，默认使用default_config
        """
        self.config = config or default_config
        self.nfs_config = self.config.nfs_config
        self.test_config = self.config.test_config
        self.logger = self._setup_logger()
        
        # 安全检查：确保只清理测试相关文件
        self.test_markers = [
            'test_mode',
            'E2E测试',
            'batch_test',
            'img-task',
            'img-batch'
        ]
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("NFSCleaner")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _is_test_generated_path(self, path: str, image_name: str) -> bool:
        """
        检查路径是否为测试生成的文件
        
        Args:
            path: 文件或目录路径
            image_name: 图像名称
            
        Returns:
            bool: 是否为测试生成的路径
        """
        # 检查图像名称是否包含测试标识
        for marker in self.test_markers:
            if marker in image_name:
                return True
        
        # 检查路径中是否包含测试标识
        path_lower = path.lower()
        for marker in self.test_markers:
            if marker.lower() in path_lower:
                return True
        
        # 检查是否为MD5格式的图像名称（测试生成的随机名称）
        if len(image_name) == 16 and all(c in '0123456789abcdef' for c in image_name):
            return True
        
        return False
    
    def _get_directory_size(self, path: str) -> int:
        """
        获取目录大小
        
        Args:
            path: 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            self.logger.warning(f"⚠️ 计算目录大小失败 {path}: {e}")
        
        return total_size
    
    def _format_size(self, size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化的大小字符串
        """
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def scan_test_files(self, project_id: str, max_age_hours: Optional[int] = None) -> Dict[str, Any]:
        """
        扫描测试生成的文件
        
        Args:
            project_id: 项目ID
            max_age_hours: 最大文件年龄（小时），None表示不限制
            
        Returns:
            Dict: 扫描结果
        """
        self.logger.info(f"🔍 扫描项目 {project_id} 的测试文件...")
        
        result = {
            'project_id': project_id,
            'scan_time': datetime.now().isoformat(),
            'test_directories': [],
            'total_size': 0,
            'total_count': 0,
            'errors': []
        }
        
        try:
            project_path = os.path.join(self.nfs_config['project_save_dir'], str(project_id))
            
            if not os.path.exists(project_path):
                self.logger.warning(f"⚠️ 项目目录不存在: {project_path}")
                return result
            
            # 扫描项目目录下的所有子目录
            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)
                
                if not os.path.isdir(item_path):
                    continue
                
                # 检查是否为测试生成的目录
                if not self._is_test_generated_path(item_path, item):
                    continue
                
                # 检查文件年龄
                if max_age_hours is not None:
                    try:
                        mtime = os.path.getmtime(item_path)
                        age_hours = (time.time() - mtime) / 3600
                        if age_hours < max_age_hours:
                            continue
                    except Exception as e:
                        self.logger.warning(f"⚠️ 检查文件年龄失败 {item_path}: {e}")
                        continue
                
                # 获取目录信息
                try:
                    dir_size = self._get_directory_size(item_path)
                    mtime = os.path.getmtime(item_path)
                    
                    dir_info = {
                        'name': item,
                        'path': item_path,
                        'size': dir_size,
                        'size_formatted': self._format_size(dir_size),
                        'modified_time': datetime.fromtimestamp(mtime).isoformat(),
                        'age_hours': (time.time() - mtime) / 3600
                    }
                    
                    result['test_directories'].append(dir_info)
                    result['total_size'] += dir_size
                    result['total_count'] += 1
                    
                except Exception as e:
                    error_msg = f"扫描目录失败 {item_path}: {e}"
                    self.logger.error(f"❌ {error_msg}")
                    result['errors'].append(error_msg)
            
            self.logger.info(f"📋 扫描完成: 发现 {result['total_count']} 个测试目录，总大小 {self._format_size(result['total_size'])}")
            
        except Exception as e:
            error_msg = f"扫描项目目录失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            result['errors'].append(error_msg)
        
        return result
    
    def preview_cleanup(self, project_id: str, max_age_hours: Optional[int] = None) -> Dict[str, Any]:
        """
        预览清理操作（不实际删除）
        
        Args:
            project_id: 项目ID
            max_age_hours: 最大文件年龄（小时）
            
        Returns:
            Dict: 预览结果
        """
        self.logger.info(f"👁️ 预览清理操作: 项目={project_id}")
        
        scan_result = self.scan_test_files(project_id, max_age_hours)
        
        preview_result = {
            'project_id': project_id,
            'preview_time': datetime.now().isoformat(),
            'will_delete': scan_result['test_directories'],
            'total_files_to_delete': scan_result['total_count'],
            'total_size_to_free': scan_result['total_size'],
            'total_size_formatted': self._format_size(scan_result['total_size']),
            'errors': scan_result['errors']
        }
        
        if preview_result['total_files_to_delete'] > 0:
            self.logger.info(f"📋 预览结果: 将删除 {preview_result['total_files_to_delete']} 个目录，释放 {preview_result['total_size_formatted']} 空间")
            
            # 显示前5个将被删除的目录
            for i, dir_info in enumerate(preview_result['will_delete'][:5]):
                self.logger.info(f"   {i+1}. {dir_info['name']} ({dir_info['size_formatted']}, {dir_info['age_hours']:.1f}小时前)")
            
            if len(preview_result['will_delete']) > 5:
                self.logger.info(f"   ... 还有 {len(preview_result['will_delete']) - 5} 个目录")
        else:
            self.logger.info("📋 预览结果: 没有找到需要清理的测试文件")
        
        return preview_result

    def cleanup_test_files(self, project_id: str, max_age_hours: Optional[int] = None,
                          dry_run: bool = False, confirm: bool = False) -> Dict[str, Any]:
        """
        清理测试生成的文件

        Args:
            project_id: 项目ID
            max_age_hours: 最大文件年龄（小时）
            dry_run: 是否为试运行（不实际删除）
            confirm: 是否已确认删除操作

        Returns:
            Dict: 清理结果
        """
        if dry_run:
            self.logger.info(f"🧪 试运行清理操作: 项目={project_id}")
            return self.preview_cleanup(project_id, max_age_hours)

        if not confirm:
            self.logger.warning("⚠️ 清理操作需要确认，请设置 confirm=True")
            return {'status': 'cancelled', 'message': '需要确认删除操作'}

        self.logger.info(f"🧹 开始清理测试文件: 项目={project_id}")

        # 先预览要删除的文件
        preview = self.preview_cleanup(project_id, max_age_hours)

        result = {
            'project_id': project_id,
            'cleanup_time': datetime.now().isoformat(),
            'deleted_directories': [],
            'failed_deletions': [],
            'total_deleted': 0,
            'total_size_freed': 0,
            'errors': []
        }

        if not preview['will_delete']:
            self.logger.info("📋 没有找到需要清理的测试文件")
            result['status'] = 'no_files'
            return result

        # 执行删除操作
        for dir_info in preview['will_delete']:
            try:
                dir_path = dir_info['path']
                dir_size = dir_info['size']

                self.logger.info(f"🗑️ 删除目录: {dir_info['name']} ({dir_info['size_formatted']})")

                # 删除目录
                shutil.rmtree(dir_path)

                # 记录成功删除的目录
                result['deleted_directories'].append({
                    'name': dir_info['name'],
                    'path': dir_path,
                    'size': dir_size,
                    'size_formatted': dir_info['size_formatted']
                })

                result['total_deleted'] += 1
                result['total_size_freed'] += dir_size

                self.logger.info(f"✅ 成功删除: {dir_info['name']}")

            except Exception as e:
                error_msg = f"删除目录失败 {dir_info['path']}: {e}"
                self.logger.error(f"❌ {error_msg}")

                result['failed_deletions'].append({
                    'name': dir_info['name'],
                    'path': dir_info['path'],
                    'error': str(e)
                })
                result['errors'].append(error_msg)

        # 设置清理状态
        if result['total_deleted'] == len(preview['will_delete']):
            result['status'] = 'success'
        elif result['total_deleted'] > 0:
            result['status'] = 'partial_success'
        else:
            result['status'] = 'failed'

        result['total_size_freed_formatted'] = self._format_size(result['total_size_freed'])

        self.logger.info(f"🎉 清理完成: 删除 {result['total_deleted']} 个目录，释放 {result['total_size_freed_formatted']} 空间")

        if result['failed_deletions']:
            self.logger.warning(f"⚠️ {len(result['failed_deletions'])} 个目录删除失败")

        return result

    def cleanup_specific_images(self, project_id: str, image_names: List[str],
                               confirm: bool = False) -> Dict[str, Any]:
        """
        清理指定图像名称的测试文件

        Args:
            project_id: 项目ID
            image_names: 要清理的图像名称列表
            confirm: 是否已确认删除操作

        Returns:
            Dict: 清理结果
        """
        if not confirm:
            self.logger.warning("⚠️ 清理操作需要确认，请设置 confirm=True")
            return {'status': 'cancelled', 'message': '需要确认删除操作'}

        self.logger.info(f"🧹 清理指定图像: 项目={project_id}, 图像数量={len(image_names)}")

        result = {
            'project_id': project_id,
            'cleanup_time': datetime.now().isoformat(),
            'target_images': image_names,
            'deleted_directories': [],
            'not_found': [],
            'failed_deletions': [],
            'total_deleted': 0,
            'total_size_freed': 0,
            'errors': []
        }

        project_path = os.path.join(self.nfs_config['project_save_dir'], str(project_id))

        if not os.path.exists(project_path):
            error_msg = f"项目目录不存在: {project_path}"
            self.logger.error(f"❌ {error_msg}")
            result['errors'].append(error_msg)
            result['status'] = 'failed'
            return result

        for image_name in image_names:
            image_path = os.path.join(project_path, image_name)

            if not os.path.exists(image_path):
                self.logger.warning(f"⚠️ 图像目录不存在: {image_name}")
                result['not_found'].append(image_name)
                continue

            # 安全检查：确保是测试生成的文件
            if not self._is_test_generated_path(image_path, image_name):
                self.logger.warning(f"⚠️ 跳过非测试文件: {image_name}")
                result['not_found'].append(image_name)
                continue

            try:
                # 获取目录大小
                dir_size = self._get_directory_size(image_path)

                self.logger.info(f"🗑️ 删除图像目录: {image_name} ({self._format_size(dir_size)})")

                # 删除目录
                shutil.rmtree(image_path)

                result['deleted_directories'].append({
                    'name': image_name,
                    'path': image_path,
                    'size': dir_size,
                    'size_formatted': self._format_size(dir_size)
                })

                result['total_deleted'] += 1
                result['total_size_freed'] += dir_size

                self.logger.info(f"✅ 成功删除: {image_name}")

            except Exception as e:
                error_msg = f"删除图像目录失败 {image_name}: {e}"
                self.logger.error(f"❌ {error_msg}")

                result['failed_deletions'].append({
                    'name': image_name,
                    'path': image_path,
                    'error': str(e)
                })
                result['errors'].append(error_msg)

        # 设置清理状态
        total_targets = len(image_names)
        if result['total_deleted'] == total_targets:
            result['status'] = 'success'
        elif result['total_deleted'] > 0:
            result['status'] = 'partial_success'
        else:
            result['status'] = 'failed'

        result['total_size_freed_formatted'] = self._format_size(result['total_size_freed'])

        self.logger.info(f"🎉 指定图像清理完成: 删除 {result['total_deleted']}/{total_targets} 个目录，释放 {result['total_size_freed_formatted']} 空间")

        return result


# 便捷函数
def preview_nfs_cleanup(project_id: str, max_age_hours: Optional[int] = None) -> Dict[str, Any]:
    """
    预览NFS清理操作的便捷函数

    Args:
        project_id: 项目ID
        max_age_hours: 最大文件年龄（小时）

    Returns:
        Dict: 预览结果

    Example:
        # 预览项目17的所有测试文件
        result = preview_nfs_cleanup('17')

        # 预览项目17中超过24小时的测试文件
        result = preview_nfs_cleanup('17', max_age_hours=24)
    """
    cleaner = NFSCleaner()
    return cleaner.preview_cleanup(project_id, max_age_hours)

def cleanup_nfs_test_files(project_id: str, max_age_hours: Optional[int] = None,
                          dry_run: bool = False, confirm: bool = False) -> Dict[str, Any]:
    """
    清理NFS测试文件的便捷函数

    Args:
        project_id: 项目ID
        max_age_hours: 最大文件年龄（小时）
        dry_run: 是否为试运行
        confirm: 是否已确认删除操作

    Returns:
        Dict: 清理结果

    Example:
        # 试运行清理（不实际删除）
        result = cleanup_nfs_test_files('17', dry_run=True)

        # 清理项目17中超过1小时的测试文件
        result = cleanup_nfs_test_files('17', max_age_hours=1, confirm=True)

        # 清理项目17的所有测试文件（需要确认）
        result = cleanup_nfs_test_files('17', confirm=True)
    """
    cleaner = NFSCleaner()
    return cleaner.cleanup_test_files(project_id, max_age_hours, dry_run, confirm)

def cleanup_specific_test_images(project_id: str, image_names: List[str],
                                confirm: bool = False) -> Dict[str, Any]:
    """
    清理指定测试图像的便捷函数

    Args:
        project_id: 项目ID
        image_names: 要清理的图像名称列表
        confirm: 是否已确认删除操作

    Returns:
        Dict: 清理结果

    Example:
        # 清理指定的测试图像
        image_names = ['0fdb86d1bdd93885', 'abc123def456']
        result = cleanup_specific_test_images('17', image_names, confirm=True)
    """
    cleaner = NFSCleaner()
    return cleaner.cleanup_specific_images(project_id, image_names, confirm)
