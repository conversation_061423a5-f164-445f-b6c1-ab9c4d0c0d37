#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NFS文件验证组件

参考image_status_calibrator/nfs_checker.py的实现方式，
创建验证模块检查NFS存储上是否生成了对应的文件，处理项目路径可能不一致的情况
"""

import os
import time
import glob
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from tests.e2e.config.config import default_config


class NFSValidator:
    """NFS文件验证器"""
    
    def __init__(self, config=None):
        """
        初始化NFS验证器
        
        Args:
            config: 配置对象，默认使用default_config
        """
        self.config = config or default_config
        self.nfs_config = self.config.nfs_config
        self.test_config = self.config.test_config
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("NFSValidator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def check_nfs_accessibility(self) -> Dict[str, Any]:
        """
        检查NFS存储可访问性
        
        Returns:
            Dict: NFS可访问性检查结果
        """
        self.logger.info("🔍 检查NFS存储可访问性...")
        
        result = {
            'status': 'unknown',
            'nfs_path': self.nfs_config['project_save_dir'],
            'accessible': False,
            'details': {}
        }
        
        try:
            nfs_path = self.nfs_config['project_save_dir']
            
            # 检查路径是否存在
            if os.path.exists(nfs_path):
                result['accessible'] = True
                
                # 获取路径信息
                stat_info = os.stat(nfs_path)
                result['details'] = {
                    'is_directory': os.path.isdir(nfs_path),
                    'is_readable': os.access(nfs_path, os.R_OK),
                    'is_writable': os.access(nfs_path, os.W_OK),
                    'size': stat_info.st_size,
                    'modified_time': datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                }
                
                # 检查是否可以列出目录内容
                if os.path.isdir(nfs_path):
                    try:
                        contents = os.listdir(nfs_path)
                        result['details']['content_count'] = len(contents)
                        result['details']['sample_contents'] = contents[:10]  # 前10个项目
                    except PermissionError:
                        result['details']['list_permission'] = False
                
                if result['details']['is_readable'] and result['details']['is_writable']:
                    result['status'] = 'accessible'
                    self.logger.info(f"✅ NFS存储可访问: {nfs_path}")
                elif result['details']['is_readable']:
                    result['status'] = 'readable_only'
                    self.logger.warning(f"⚠️ NFS存储只读权限: {nfs_path}")
                else:
                    result['status'] = 'permission_denied'
                    self.logger.warning(f"⚠️ NFS存储权限不足: {nfs_path}")
            else:
                result['status'] = 'not_found'
                self.logger.error(f"❌ NFS存储路径不存在: {nfs_path}")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ NFS存储可访问性检查失败: {e}")
        
        return result
    
    def check_type1_2_files(self, project_id: str, image_name: str) -> Dict[str, Any]:
        """
        检查普通图片（Type 1）和 Dicom 图片（Type 2）的文件状态
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            
        Returns:
            Dict: Type 1/2 文件检查结果
        """
        self.logger.info(f"🔍 检查Type 1/2文件: 项目={project_id}, 图片={image_name}")
        
        result = {
            'status': 'unknown',
            'project_id': project_id,
            'image_name': image_name,
            'files': {},
            'complete': False
        }
        
        try:
            # 构建项目路径
            project_path = os.path.join(self.nfs_config['project_save_dir'], str(project_id))
            
            # 构建文件路径
            main_image_path = os.path.join(project_path, f"{image_name}.png")
            thumbnail_path = os.path.join(project_path, "thumbnail", f"{image_name}.png")
            
            # 检查主图片
            main_exists = os.path.exists(main_image_path)
            result['files']['main_image'] = {
                'path': main_image_path,
                'exists': main_exists,
                'size': os.path.getsize(main_image_path) if main_exists else 0
            }
            
            # 检查缩略图
            thumbnail_exists = os.path.exists(thumbnail_path)
            result['files']['thumbnail'] = {
                'path': thumbnail_path,
                'exists': thumbnail_exists,
                'size': os.path.getsize(thumbnail_path) if thumbnail_exists else 0
            }
            
            # 判断完整性
            result['complete'] = main_exists and thumbnail_exists
            
            if result['complete']:
                result['status'] = 'complete'
                self.logger.info(f"✅ Type 1/2文件完整")
            else:
                result['status'] = 'incomplete'
                missing_files = []
                if not main_exists:
                    missing_files.append('主图片')
                if not thumbnail_exists:
                    missing_files.append('缩略图')
                self.logger.warning(f"⚠️ Type 1/2文件不完整，缺少: {', '.join(missing_files)}")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Type 1/2文件检查失败: {e}")
        
        return result
    
    def check_type3_files(self, project_id: str, image_name: str) -> Dict[str, Any]:
        """
        检查病理图片（Type 3）的文件状态
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            
        Returns:
            Dict: Type 3 文件检查结果
        """
        self.logger.info(f"🔍 检查Type 3文件: 项目={project_id}, 图片={image_name}")
        
        result = {
            'status': 'unknown',
            'project_id': project_id,
            'image_name': image_name,
            'files': {},
            'complete': False
        }
        
        try:
            # 构建项目路径
            project_path = os.path.join(self.nfs_config['project_save_dir'], str(project_id))
            
            # 构建 deepzoom metadata.xml 路径
            metadata_path = os.path.join(project_path, image_name, "deepzoom", "metadata.xml")
            
            # 检查 metadata.xml 文件
            metadata_exists = os.path.exists(metadata_path)
            result['files']['metadata'] = {
                'path': metadata_path,
                'exists': metadata_exists,
                'size': os.path.getsize(metadata_path) if metadata_exists else 0
            }
            
            # 检查deepzoom目录结构
            deepzoom_dir = os.path.join(project_path, image_name, "deepzoom")
            if os.path.exists(deepzoom_dir):
                try:
                    deepzoom_contents = os.listdir(deepzoom_dir)
                    result['files']['deepzoom_contents'] = deepzoom_contents
                    
                    # 检查是否有切片文件夹
                    level_dirs = [d for d in deepzoom_contents if d.isdigit()]
                    result['files']['level_count'] = len(level_dirs)
                    
                except Exception:
                    result['files']['deepzoom_contents'] = []
                    result['files']['level_count'] = 0
            
            # 判断完整性
            result['complete'] = metadata_exists
            
            if result['complete']:
                result['status'] = 'complete'
                self.logger.info(f"✅ Type 3文件完整")
            else:
                result['status'] = 'incomplete'
                self.logger.warning(f"⚠️ Type 3文件不完整，metadata.xml不存在")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Type 3文件检查失败: {e}")
        
        return result
    
    def check_type4_files(self, project_id: str, image_name: str) -> Dict[str, Any]:
        """
        检查多通道图片（Type 4）的文件状态
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            
        Returns:
            Dict: Type 4 文件检查结果
        """
        self.logger.info(f"🔍 检查Type 4文件: 项目={project_id}, 图片={image_name}")
        
        result = {
            'status': 'unknown',
            'project_id': project_id,
            'image_name': image_name,
            'files': {},
            'channels': {},
            'complete': False
        }
        
        try:
            # 构建项目路径
            project_path = os.path.join(self.nfs_config['project_save_dir'], str(project_id))
            
            # 构建 split 目录路径
            split_dir = os.path.join(project_path, image_name, "split")
            
            # 查找 channel_*.tiff 文件
            channel_pattern = os.path.join(split_dir, "channel_*.tiff")
            channel_files = glob.glob(channel_pattern)
            
            result['files']['split_dir'] = {
                'path': split_dir,
                'exists': os.path.exists(split_dir),
                'channel_files': channel_files,
                'channel_count': len(channel_files)
            }
            
            if not channel_files:
                result['status'] = 'incomplete'
                result['complete'] = False
                self.logger.warning(f"⚠️ Type 4文件不完整，未找到channel_*.tiff文件")
                return result
            
            # 检查每个通道的 deepzoom metadata
            all_channels_complete = True
            
            for channel_file in channel_files:
                # 从文件名提取通道号
                channel_filename = os.path.basename(channel_file)
                try:
                    channel_num = channel_filename.replace("channel_", "").replace(".tiff", "")
                    
                    # 构建对应通道的 deepzoom metadata 路径
                    channel_metadata_path = os.path.join(
                        project_path, image_name, channel_num, "deepzoom", "metadata.xml"
                    )
                    
                    metadata_exists = os.path.exists(channel_metadata_path)
                    
                    result['channels'][channel_num] = {
                        'channel_file': channel_file,
                        'metadata_path': channel_metadata_path,
                        'metadata_exists': metadata_exists,
                        'metadata_size': os.path.getsize(channel_metadata_path) if metadata_exists else 0
                    }
                    
                    if not metadata_exists:
                        all_channels_complete = False
                        self.logger.warning(f"⚠️ 通道 {channel_num} 的metadata.xml不存在")
                    else:
                        self.logger.info(f"✅ 通道 {channel_num} 的metadata.xml存在")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ 解析通道文件名失败: {channel_file}, 错误: {e}")
                    all_channels_complete = False
            
            # 判断完整性
            result['complete'] = all_channels_complete and len(channel_files) > 0
            
            if result['complete']:
                result['status'] = 'complete'
                self.logger.info(f"✅ Type 4文件完整，所有 {len(channel_files)} 个通道都有对应的metadata.xml")
            else:
                result['status'] = 'incomplete'
                self.logger.warning(f"⚠️ Type 4文件不完整")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Type 4文件检查失败: {e}")
        
        return result
    
    def check_image_files(self, project_id: str, image_name: str, image_type_id: int) -> Dict[str, Any]:
        """
        根据图片类型检查对应的文件
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            image_type_id: 图片类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道)
            
        Returns:
            Dict: 文件检查结果
        """
        self.logger.info(f"🔍 检查图像文件: 项目={project_id}, 图片={image_name}, 类型={image_type_id}")
        
        if image_type_id in [1, 2]:
            return self.check_type1_2_files(project_id, image_name)
        elif image_type_id == 3:
            return self.check_type3_files(project_id, image_name)
        elif image_type_id == 4:
            return self.check_type4_files(project_id, image_name)
        else:
            return {
                'status': 'unsupported',
                'project_id': project_id,
                'image_name': image_name,
                'image_type_id': image_type_id,
                'error': f'不支持的图片类型ID: {image_type_id}'
            }
    
    def monitor_file_generation(self, project_id: str, image_name: str, 
                              image_type_id: int, timeout: int = 300) -> Dict[str, Any]:
        """
        监控文件生成过程
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            image_type_id: 图片类型ID
            timeout: 监控超时时间（秒）
            
        Returns:
            Dict: 文件生成监控结果
        """
        self.logger.info(f"🔍 监控文件生成: 项目={project_id}, 图片={image_name}, 类型={image_type_id}")
        
        result = {
            'status': 'unknown',
            'project_id': project_id,
            'image_name': image_name,
            'image_type_id': image_type_id,
            'monitoring_time': 0,
            'checks': [],
            'final_complete': False
        }
        
        try:
            start_time = time.time()
            check_interval = 10  # 每10秒检查一次
            
            while time.time() - start_time < timeout:
                current_time = time.time() - start_time
                
                # 检查文件状态
                check_result = self.check_image_files(project_id, image_name, image_type_id)
                
                check_entry = {
                    'timestamp': current_time,
                    'status': check_result['status'],
                    'complete': check_result.get('complete', False)
                }
                
                result['checks'].append(check_entry)
                
                self.logger.info(f"📋 文件检查 (耗时: {current_time:.1f}s): {check_result['status']}")
                
                # 如果文件已完整，结束监控
                if check_result.get('complete', False):
                    result['final_complete'] = True
                    result['status'] = 'completed'
                    self.logger.info(f"✅ 文件生成完成，耗时: {current_time:.1f}秒")
                    break
                
                time.sleep(check_interval)
            
            result['monitoring_time'] = time.time() - start_time
            
            if result['status'] == 'unknown':
                result['status'] = 'timeout'
                self.logger.warning(f"⚠️ 文件生成监控超时: {timeout}秒")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ 文件生成监控失败: {e}")
        
        return result
    
    def validate_nfs_system(self, test_project_id: str = "17") -> Dict[str, Any]:
        """
        综合验证NFS系统

        Args:
            test_project_id: 用于测试的项目ID

        Returns:
            Dict: 综合验证结果
        """
        self.logger.info("🎯 开始NFS系统综合验证...")

        validation_result = {
            'overall_status': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'summary': {}
        }

        # 1. 检查NFS根目录可访问性
        accessibility_result = self.check_nfs_accessibility()
        validation_result['checks']['root_accessibility'] = accessibility_result

        # 2. 检查具体项目目录权限
        project_path = os.path.join(self.nfs_config['project_save_dir'], test_project_id)
        project_result = self._check_project_directory(project_path, test_project_id)
        validation_result['checks']['project_accessibility'] = project_result

        # 生成综合状态
        root_ok = accessibility_result['status'] in ['accessible', 'readable_only']
        project_ok = project_result['status'] == 'accessible'

        if root_ok and project_ok:
            validation_result['overall_status'] = 'healthy'
            self.logger.info("✅ NFS系统验证通过 - 系统健康")
        elif root_ok or project_ok:
            validation_result['overall_status'] = 'warning'
            self.logger.warning("⚠️ NFS系统验证部分通过 - 系统存在问题")
        else:
            validation_result['overall_status'] = 'error'
            self.logger.error("❌ NFS系统验证失败 - 系统异常")

        # 生成摘要
        validation_result['summary'] = {
            'nfs_path': self.nfs_config['project_save_dir'],
            'test_project_id': test_project_id,
            'root_accessibility_status': accessibility_result.get('status', 'unknown'),
            'project_accessibility_status': project_result.get('status', 'unknown'),
            'root_readable': accessibility_result.get('details', {}).get('is_readable', False),
            'root_writable': accessibility_result.get('details', {}).get('is_writable', False),
            'project_readable': project_result.get('details', {}).get('is_readable', False),
            'project_writable': project_result.get('details', {}).get('is_writable', False)
        }

        self.logger.info("🎉 NFS系统验证完成")
        return validation_result

    def _check_project_directory(self, project_path: str, project_id: str) -> Dict[str, Any]:
        """
        检查项目目录的可访问性

        Args:
            project_path: 项目目录路径
            project_id: 项目ID

        Returns:
            Dict: 项目目录检查结果
        """
        self.logger.info(f"🔍 检查项目目录可访问性: {project_path}")

        result = {
            'status': 'unknown',
            'project_path': project_path,
            'project_id': project_id,
            'accessible': False,
            'details': {}
        }

        try:
            # 检查路径是否存在
            if os.path.exists(project_path):
                result['accessible'] = True

                # 获取路径信息
                stat_info = os.stat(project_path)
                result['details'] = {
                    'is_directory': os.path.isdir(project_path),
                    'is_readable': os.access(project_path, os.R_OK),
                    'is_writable': os.access(project_path, os.W_OK),
                    'size': stat_info.st_size,
                    'modified_time': datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                }

                # 检查是否可以列出目录内容
                if os.path.isdir(project_path):
                    try:
                        contents = os.listdir(project_path)
                        result['details']['content_count'] = len(contents)
                        result['details']['sample_contents'] = contents[:5]  # 前5个项目
                    except PermissionError:
                        result['details']['list_permission'] = False

                if result['details']['is_readable'] and result['details']['is_writable']:
                    result['status'] = 'accessible'
                    self.logger.info(f"✅ 项目目录可访问: {project_path}")
                elif result['details']['is_readable']:
                    result['status'] = 'readable_only'
                    self.logger.warning(f"⚠️ 项目目录只读权限: {project_path}")
                else:
                    result['status'] = 'permission_denied'
                    self.logger.warning(f"⚠️ 项目目录权限不足: {project_path}")
            else:
                result['status'] = 'not_found'
                self.logger.warning(f"⚠️ 项目目录不存在: {project_path}")

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ 项目目录可访问性检查失败: {e}")

        return result


# 便捷函数
def validate_nfs(config=None) -> Dict[str, Any]:
    """验证NFS系统的便捷函数"""
    validator = NFSValidator(config)
    return validator.validate_nfs_system()


def check_image_files(project_id: str, image_name: str, image_type_id: int, config=None) -> Dict[str, Any]:
    """检查图像文件的便捷函数"""
    validator = NFSValidator(config)
    return validator.check_image_files(project_id, image_name, image_type_id)
