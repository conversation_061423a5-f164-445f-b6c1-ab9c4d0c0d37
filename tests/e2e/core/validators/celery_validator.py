#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Celery验证组件

验证Celery工作进程是否正常运行，验证任务是否能正确入队和执行
"""

import time
import logging
import subprocess
import psutil
import pika
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from tests.e2e.config.config import default_config


class CeleryValidator:
    """Celery验证器"""
    
    def __init__(self, config=None):
        """
        初始化Celery验证器
        
        Args:
            config: 配置对象，默认使用default_config
        """
        self.config = config or default_config
        self.rabbitmq_config = self.config.rabbitmq_config
        self.queue_config = self.config.queue_config
        self.test_config = self.config.test_config
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("CeleryValidator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def check_celery_processes(self) -> Dict[str, any]:
        """
        检查Celery进程状态
        
        Returns:
            Dict: 进程检查结果
        """
        self.logger.info("🔍 检查Celery进程状态...")
        
        result = {
            'status': 'unknown',
            'processes': [],
            'worker_count': 0,
            'details': {}
        }
        
        try:
            # 查找Celery相关进程
            celery_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'create_time']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    
                    # 检查是否是Celery worker进程
                    if 'celery' in cmdline.lower() and 'worker' in cmdline.lower():
                        process_info = {
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'status': proc.info['status'],
                            'create_time': datetime.fromtimestamp(proc.info['create_time']).isoformat(),
                            'is_worker': True
                        }
                        celery_processes.append(process_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            result['processes'] = celery_processes
            result['worker_count'] = len(celery_processes)
            
            if celery_processes:
                result['status'] = 'running'
                self.logger.info(f"✅ 发现 {len(celery_processes)} 个Celery worker进程")
                
                for proc in celery_processes:
                    self.logger.info(f"   📋 PID: {proc['pid']}, 状态: {proc['status']}")
            else:
                result['status'] = 'not_running'
                self.logger.warning("⚠️ 未发现运行中的Celery worker进程")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ 检查Celery进程时发生错误: {e}")
        
        return result
    
    def check_queue_connection(self) -> Dict[str, any]:
        """
        检查队列连接状态
        
        Returns:
            Dict: 连接检查结果
        """
        self.logger.info("🔍 检查RabbitMQ队列连接...")
        
        result = {
            'status': 'unknown',
            'connection_time': None,
            'queues_info': {},
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 创建连接
            credentials = pika.PlainCredentials(
                self.rabbitmq_config['username'],
                self.rabbitmq_config['password']
            )
            
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(
                    host=self.rabbitmq_config['host'],
                    port=self.rabbitmq_config['port'],
                    virtual_host=self.rabbitmq_config['virtual_host'],
                    credentials=credentials,
                    heartbeat=self.rabbitmq_config['heartbeat']
                )
            )
            
            channel = connection.channel()
            connection_time = time.time() - start_time
            result['connection_time'] = connection_time
            
            # 检查关键队列
            queues_to_check = [
                self.queue_config['image_convert_queue'],
                self.queue_config['ailabel_queue']
            ]
            
            for queue_name in queues_to_check:
                try:
                    method = channel.queue_declare(queue=queue_name, passive=True)
                    queue_info = {
                        'exists': True,
                        'message_count': method.method.message_count,
                        'consumer_count': method.method.consumer_count
                    }
                    result['queues_info'][queue_name] = queue_info
                    
                    self.logger.info(f"✅ 队列 {queue_name}: 消息数={queue_info['message_count']}, 消费者数={queue_info['consumer_count']}")
                    
                except pika.exceptions.ChannelClosedByBroker:
                    # 队列不存在
                    result['queues_info'][queue_name] = {
                        'exists': False,
                        'message_count': 0,
                        'consumer_count': 0
                    }
                    self.logger.warning(f"⚠️ 队列 {queue_name} 不存在")
                    
                    # 重新创建channel
                    channel = connection.channel()
            
            connection.close()
            
            result['status'] = 'connected'
            self.logger.info(f"✅ RabbitMQ连接成功，耗时: {connection_time:.2f}秒")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ RabbitMQ连接失败: {e}")
        
        return result
    
    def check_task_processing_capability(self, timeout: int = 60) -> Dict[str, any]:
        """
        检查任务处理能力（发送测试任务并监控处理）
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            Dict: 任务处理能力检查结果
        """
        self.logger.info("🔍 检查任务处理能力...")
        
        result = {
            'status': 'unknown',
            'test_task_sent': False,
            'task_processed': False,
            'processing_time': None,
            'details': {}
        }
        
        try:
            # 创建连接
            credentials = pika.PlainCredentials(
                self.rabbitmq_config['username'],
                self.rabbitmq_config['password']
            )
            
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(
                    host=self.rabbitmq_config['host'],
                    port=self.rabbitmq_config['port'],
                    virtual_host=self.rabbitmq_config['virtual_host'],
                    credentials=credentials,
                    heartbeat=0  # 禁用心跳以避免测试期间连接问题
                )
            )
            
            channel = connection.channel()
            
            # 获取队列初始状态
            queue_name = self.queue_config['image_convert_queue']
            initial_method = channel.queue_declare(queue=queue_name, passive=True)
            initial_message_count = initial_method.method.message_count
            
            # 创建简单的测试任务
            test_task = {
                "taskId": f"celery-test-{int(time.time())}",
                "imageTypeId": 1,  # 简单图像类型
                "projectId": "test",
                "imageName": "celery_test_image",
                "imageId": f"test-img-{int(time.time())}",
                "imageUrl": "/tmp/test_image.jpg",  # 测试路径
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "test_mode": True,
                    "celery_validation": True,
                    "source": "CeleryValidator"
                }
            }
            
            # 发送测试任务
            start_time = time.time()
            channel.basic_publish(
                exchange='',
                routing_key=queue_name,
                body=json.dumps(test_task),
                properties=pika.BasicProperties(
                    delivery_mode=2,
                    content_type='application/json',
                    content_encoding='utf-8'
                )
            )
            
            result['test_task_sent'] = True
            self.logger.info(f"✅ 测试任务已发送: {test_task['taskId']}")
            
            # 监控任务处理
            processed = False
            elapsed_time = 0
            check_interval = 2  # 每2秒检查一次
            
            while elapsed_time < timeout and not processed:
                time.sleep(check_interval)
                elapsed_time += check_interval
                
                try:
                    # 检查队列消息数量变化
                    current_method = channel.queue_declare(queue=queue_name, passive=True)
                    current_message_count = current_method.method.message_count
                    
                    # 如果消息数量减少，说明任务被处理了
                    if current_message_count < initial_message_count + 1:
                        processed = True
                        processing_time = time.time() - start_time
                        result['task_processed'] = True
                        result['processing_time'] = processing_time
                        
                        self.logger.info(f"✅ 测试任务已被处理，耗时: {processing_time:.2f}秒")
                        break
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ 监控队列时发生错误: {e}")
                    break
            
            if not processed:
                self.logger.warning(f"⚠️ 测试任务在 {timeout} 秒内未被处理")
                result['status'] = 'timeout'
            else:
                result['status'] = 'success'
            
            connection.close()
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ 任务处理能力检查失败: {e}")
        
        return result
    
    def validate_celery_system(self) -> Dict[str, any]:
        """
        综合验证Celery系统
        
        Returns:
            Dict: 综合验证结果
        """
        self.logger.info("🎯 开始Celery系统综合验证...")
        
        validation_result = {
            'overall_status': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'summary': {}
        }
        
        # 1. 检查进程状态
        process_result = self.check_celery_processes()
        validation_result['checks']['processes'] = process_result
        
        # 2. 检查队列连接
        connection_result = self.check_queue_connection()
        validation_result['checks']['connection'] = connection_result
        
        # 3. 检查任务处理能力（仅在进程和连接都正常时进行）
        if (process_result['status'] == 'running' and 
            connection_result['status'] == 'connected'):
            
            processing_result = self.check_task_processing_capability()
            validation_result['checks']['processing'] = processing_result
        else:
            self.logger.warning("⚠️ 跳过任务处理能力检查（前置条件不满足）")
            validation_result['checks']['processing'] = {
                'status': 'skipped',
                'reason': 'Prerequisites not met'
            }
        
        # 生成综合状态
        all_checks = validation_result['checks']
        
        if all(check.get('status') in ['running', 'connected', 'success'] 
               for check in all_checks.values() if check.get('status') != 'skipped'):
            validation_result['overall_status'] = 'healthy'
            self.logger.info("✅ Celery系统验证通过 - 系统健康")
        elif any(check.get('status') == 'error' for check in all_checks.values()):
            validation_result['overall_status'] = 'error'
            self.logger.error("❌ Celery系统验证失败 - 系统异常")
        else:
            validation_result['overall_status'] = 'warning'
            self.logger.warning("⚠️ Celery系统验证部分通过 - 系统存在问题")
        
        # 生成摘要
        validation_result['summary'] = {
            'worker_count': process_result.get('worker_count', 0),
            'connection_status': connection_result.get('status', 'unknown'),
            'processing_status': all_checks['processing'].get('status', 'unknown'),
            'total_checks': len(all_checks),
            'passed_checks': sum(1 for check in all_checks.values() 
                               if check.get('status') in ['running', 'connected', 'success'])
        }
        
        self.logger.info("🎉 Celery系统验证完成")
        return validation_result


# 便捷函数
def validate_celery(config=None) -> Dict[str, any]:
    """验证Celery系统的便捷函数"""
    validator = CeleryValidator(config)
    return validator.validate_celery_system()
