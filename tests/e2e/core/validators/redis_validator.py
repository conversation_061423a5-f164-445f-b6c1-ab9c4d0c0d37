#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis验证组件

验证Redis连接和数据存储，检查任务队列中是否有相应的数据，验证任务状态是否正确更新
"""

import time
import json
import logging
import redis
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from tests.e2e.config.config import default_config


class RedisValidator:
    """Redis验证器"""
    
    def __init__(self, config=None):
        """
        初始化Redis验证器
        
        Args:
            config: 配置对象，默认使用default_config
        """
        self.config = config or default_config
        self.redis_config = self.config.redis_config
        self.test_config = self.config.test_config
        self.logger = self._setup_logger()
        self.redis_client = None
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("RedisValidator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _get_redis_client(self) -> redis.Redis:
        """获取Redis客户端连接"""
        if self.redis_client is None:
            self.redis_client = redis.Redis(
                host=self.redis_config['host'],
                port=self.redis_config['port'],
                db=self.redis_config['db'],
                decode_responses=True,
                socket_timeout=10,
                socket_connect_timeout=10,
                retry_on_timeout=True
            )
        return self.redis_client
    
    def check_redis_connection(self) -> Dict[str, Any]:
        """
        检查Redis连接状态
        
        Returns:
            Dict: 连接检查结果
        """
        self.logger.info("🔍 检查Redis连接状态...")
        
        result = {
            'status': 'unknown',
            'connection_time': None,
            'server_info': {},
            'details': {}
        }
        
        try:
            start_time = time.time()
            client = self._get_redis_client()
            
            # 测试连接
            ping_result = client.ping()
            connection_time = time.time() - start_time
            
            if ping_result:
                result['status'] = 'connected'
                result['connection_time'] = connection_time
                
                # 获取服务器信息
                info = client.info()
                result['server_info'] = {
                    'redis_version': info.get('redis_version'),
                    'used_memory_human': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'total_commands_processed': info.get('total_commands_processed'),
                    'keyspace_hits': info.get('keyspace_hits'),
                    'keyspace_misses': info.get('keyspace_misses'),
                    'uptime_in_seconds': info.get('uptime_in_seconds')
                }
                
                self.logger.info(f"✅ Redis连接成功，耗时: {connection_time:.3f}秒")
                self.logger.info(f"📋 Redis版本: {info.get('redis_version')}")
                self.logger.info(f"📋 内存使用: {info.get('used_memory_human')}")
                self.logger.info(f"📋 连接客户端数: {info.get('connected_clients')}")
            else:
                result['status'] = 'failed'
                self.logger.error("❌ Redis ping失败")
                
        except redis.ConnectionError as e:
            result['status'] = 'connection_error'
            result['error'] = str(e)
            self.logger.error(f"❌ Redis连接错误: {e}")
        except redis.TimeoutError as e:
            result['status'] = 'timeout'
            result['error'] = str(e)
            self.logger.error(f"❌ Redis连接超时: {e}")
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Redis连接检查失败: {e}")
        
        return result
    
    def check_data_operations(self) -> Dict[str, Any]:
        """
        检查Redis数据操作
        
        Returns:
            Dict: 数据操作检查结果
        """
        self.logger.info("🔍 检查Redis数据操作...")
        
        result = {
            'status': 'unknown',
            'operations': {},
            'details': {}
        }
        
        try:
            client = self._get_redis_client()
            test_key_prefix = f"e2e_test_{int(time.time())}"
            
            # 测试基本操作
            operations = {}
            
            # 1. 字符串操作
            string_key = f"{test_key_prefix}_string"
            test_value = f"test_value_{int(time.time())}"
            
            start_time = time.time()
            client.set(string_key, test_value, ex=60)  # 60秒过期
            set_time = time.time() - start_time
            
            start_time = time.time()
            retrieved_value = client.get(string_key)
            get_time = time.time() - start_time
            
            operations['string'] = {
                'set_success': retrieved_value == test_value,
                'set_time': set_time,
                'get_time': get_time
            }
            
            # 2. 哈希操作
            hash_key = f"{test_key_prefix}_hash"
            hash_data = {
                'task_id': f'test_task_{int(time.time())}',
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            start_time = time.time()
            client.hset(hash_key, mapping=hash_data)
            client.expire(hash_key, 60)
            hset_time = time.time() - start_time
            
            start_time = time.time()
            retrieved_hash = client.hgetall(hash_key)
            hget_time = time.time() - start_time
            
            operations['hash'] = {
                'set_success': all(retrieved_hash.get(k) == v for k, v in hash_data.items()),
                'set_time': hset_time,
                'get_time': hget_time,
                'field_count': len(retrieved_hash)
            }
            
            # 3. 列表操作
            list_key = f"{test_key_prefix}_list"
            list_items = [f'item_{i}' for i in range(5)]
            
            start_time = time.time()
            client.lpush(list_key, *list_items)
            client.expire(list_key, 60)
            lpush_time = time.time() - start_time
            
            start_time = time.time()
            retrieved_list = client.lrange(list_key, 0, -1)
            lrange_time = time.time() - start_time
            
            operations['list'] = {
                'push_success': len(retrieved_list) == len(list_items),
                'push_time': lpush_time,
                'range_time': lrange_time,
                'item_count': len(retrieved_list)
            }
            
            # 4. 集合操作
            set_key = f"{test_key_prefix}_set"
            set_items = {f'member_{i}' for i in range(3)}
            
            start_time = time.time()
            client.sadd(set_key, *set_items)
            client.expire(set_key, 60)
            sadd_time = time.time() - start_time
            
            start_time = time.time()
            retrieved_set = client.smembers(set_key)
            smembers_time = time.time() - start_time
            
            operations['set'] = {
                'add_success': retrieved_set == set_items,
                'add_time': sadd_time,
                'members_time': smembers_time,
                'member_count': len(retrieved_set)
            }
            
            result['operations'] = operations
            
            # 检查所有操作是否成功
            all_success = all(
                op.get('set_success', op.get('push_success', op.get('add_success', False)))
                for op in operations.values()
            )
            
            if all_success:
                result['status'] = 'success'
                self.logger.info("✅ Redis数据操作测试通过")
            else:
                result['status'] = 'partial_failure'
                self.logger.warning("⚠️ Redis数据操作部分失败")
            
            # 清理测试数据
            test_keys = [string_key, hash_key, list_key, set_key]
            deleted_count = client.delete(*test_keys)
            self.logger.info(f"🧹 清理了 {deleted_count} 个测试键")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Redis数据操作检查失败: {e}")
        
        return result
    
    def check_celery_task_storage(self) -> Dict[str, Any]:
        """
        检查Celery任务存储（Redis作为结果后端）
        
        Returns:
            Dict: Celery任务存储检查结果
        """
        self.logger.info("🔍 检查Celery任务存储...")
        
        result = {
            'status': 'unknown',
            'celery_keys': [],
            'task_count': 0,
            'details': {}
        }
        
        try:
            client = self._get_redis_client()
            
            # 查找Celery相关的键
            celery_patterns = [
                'celery-task-meta-*',  # 任务元数据
                'celery-taskset-meta-*',  # 任务集元数据
                '_kombu.binding.*',  # Kombu绑定
                'unacked_mutex',  # 未确认任务互斥锁
            ]
            
            all_celery_keys = []
            for pattern in celery_patterns:
                keys = client.keys(pattern)
                all_celery_keys.extend(keys)
            
            result['celery_keys'] = all_celery_keys
            result['task_count'] = len([k for k in all_celery_keys if 'task-meta' in k])
            
            # 分析任务状态
            task_states = {}
            recent_tasks = []
            
            for key in all_celery_keys:
                if 'task-meta' in key:
                    try:
                        task_data = client.get(key)
                        if task_data:
                            task_info = json.loads(task_data)
                            state = task_info.get('status', 'UNKNOWN')
                            task_states[state] = task_states.get(state, 0) + 1
                            
                            # 收集最近的任务信息
                            if len(recent_tasks) < 5:
                                task_id = key.replace('celery-task-meta-', '')
                                recent_tasks.append({
                                    'task_id': task_id,
                                    'status': state,
                                    'result': task_info.get('result'),
                                    'traceback': task_info.get('traceback')
                                })
                    except (json.JSONDecodeError, Exception):
                        continue
            
            result['task_states'] = task_states
            result['recent_tasks'] = recent_tasks
            
            if all_celery_keys:
                result['status'] = 'found'
                self.logger.info(f"✅ 发现 {len(all_celery_keys)} 个Celery相关键")
                self.logger.info(f"📋 任务数量: {result['task_count']}")
                
                if task_states:
                    self.logger.info("📋 任务状态分布:")
                    for state, count in task_states.items():
                        self.logger.info(f"   {state}: {count}")
            else:
                result['status'] = 'empty'
                self.logger.info("📋 未发现Celery相关数据")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ Celery任务存储检查失败: {e}")
        
        return result
    
    def monitor_task_lifecycle(self, task_id: str, timeout: int = 60) -> Dict[str, Any]:
        """
        监控任务生命周期
        
        Args:
            task_id: 任务ID
            timeout: 监控超时时间（秒）
            
        Returns:
            Dict: 任务生命周期监控结果
        """
        self.logger.info(f"🔍 监控任务生命周期: {task_id}")
        
        result = {
            'status': 'unknown',
            'task_id': task_id,
            'lifecycle': [],
            'final_state': None,
            'monitoring_time': 0
        }
        
        try:
            client = self._get_redis_client()
            task_key = f'celery-task-meta-{task_id}'
            
            start_time = time.time()
            last_state = None
            check_interval = 2  # 每2秒检查一次
            
            while time.time() - start_time < timeout:
                try:
                    task_data = client.get(task_key)
                    current_time = time.time() - start_time
                    
                    if task_data:
                        task_info = json.loads(task_data)
                        current_state = task_info.get('status', 'UNKNOWN')
                        
                        # 记录状态变化
                        if current_state != last_state:
                            lifecycle_entry = {
                                'timestamp': current_time,
                                'state': current_state,
                                'result': task_info.get('result'),
                                'traceback': task_info.get('traceback')
                            }
                            result['lifecycle'].append(lifecycle_entry)
                            
                            self.logger.info(f"📋 任务状态变化: {current_state} (耗时: {current_time:.2f}s)")
                            last_state = current_state
                        
                        # 检查是否为终态
                        if current_state in ['SUCCESS', 'FAILURE', 'REVOKED']:
                            result['final_state'] = current_state
                            result['status'] = 'completed'
                            break
                    else:
                        # 任务元数据不存在
                        if last_state is None:
                            # 首次检查就没有数据
                            lifecycle_entry = {
                                'timestamp': current_time,
                                'state': 'NOT_FOUND',
                                'result': None,
                                'traceback': None
                            }
                            result['lifecycle'].append(lifecycle_entry)
                            last_state = 'NOT_FOUND'
                
                except json.JSONDecodeError:
                    self.logger.warning(f"⚠️ 任务数据格式错误: {task_id}")
                
                time.sleep(check_interval)
            
            result['monitoring_time'] = time.time() - start_time
            
            if result['status'] == 'unknown':
                result['status'] = 'timeout'
                self.logger.warning(f"⚠️ 任务监控超时: {task_id}")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.logger.error(f"❌ 任务生命周期监控失败: {e}")
        
        return result
    
    def validate_redis_system(self) -> Dict[str, Any]:
        """
        综合验证Redis系统
        
        Returns:
            Dict: 综合验证结果
        """
        self.logger.info("🎯 开始Redis系统综合验证...")
        
        validation_result = {
            'overall_status': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'summary': {}
        }
        
        # 1. 检查连接状态
        connection_result = self.check_redis_connection()
        validation_result['checks']['connection'] = connection_result
        
        # 2. 检查数据操作（仅在连接正常时进行）
        if connection_result['status'] == 'connected':
            data_result = self.check_data_operations()
            validation_result['checks']['data_operations'] = data_result
            
            # 3. 检查Celery任务存储
            celery_storage_result = self.check_celery_task_storage()
            validation_result['checks']['celery_storage'] = celery_storage_result
        else:
            self.logger.warning("⚠️ 跳过数据操作和Celery存储检查（连接失败）")
            validation_result['checks']['data_operations'] = {
                'status': 'skipped',
                'reason': 'Connection failed'
            }
            validation_result['checks']['celery_storage'] = {
                'status': 'skipped',
                'reason': 'Connection failed'
            }
        
        # 生成综合状态
        all_checks = validation_result['checks']
        
        if all(check.get('status') in ['connected', 'success', 'found', 'empty'] 
               for check in all_checks.values() if check.get('status') != 'skipped'):
            validation_result['overall_status'] = 'healthy'
            self.logger.info("✅ Redis系统验证通过 - 系统健康")
        elif any(check.get('status') in ['error', 'connection_error', 'timeout'] 
                for check in all_checks.values()):
            validation_result['overall_status'] = 'error'
            self.logger.error("❌ Redis系统验证失败 - 系统异常")
        else:
            validation_result['overall_status'] = 'warning'
            self.logger.warning("⚠️ Redis系统验证部分通过 - 系统存在问题")
        
        # 生成摘要
        validation_result['summary'] = {
            'connection_status': connection_result.get('status', 'unknown'),
            'data_operations_status': all_checks['data_operations'].get('status', 'unknown'),
            'celery_storage_status': all_checks['celery_storage'].get('status', 'unknown'),
            'celery_task_count': all_checks['celery_storage'].get('task_count', 0),
            'total_checks': len(all_checks),
            'passed_checks': sum(1 for check in all_checks.values() 
                               if check.get('status') in ['connected', 'success', 'found', 'empty'])
        }
        
        self.logger.info("🎉 Redis系统验证完成")
        return validation_result


# 便捷函数
def validate_redis(config=None) -> Dict[str, Any]:
    """验证Redis系统的便捷函数"""
    validator = RedisValidator(config)
    return validator.validate_redis_system()
