# AiLabel端到端测试基础设施

这是一个完整的端到端测试基础设施，用于验证AiLabel图像处理系统的各个组件是否正常工作。

## ✨ 新版本特性

### 重新组织的目录结构
- **模块化设计**：核心组件按功能分类（验证器、发送器、运行器）
- **清晰的输出管理**：测试报告和日志分别存储在专门目录
- **更好的可维护性**：每个组件都有独立的命名空间
- **标准化接口**：统一的导入和使用方式

### 改进的用户体验
- **统一入口**：新的 `run_tests.py` 提供更直观的命令行接口
- **灵活的测试选项**：支持单独运行各种类型的测试
- **更好的输出控制**：支持 verbose 和 quiet 模式
- **向后兼容**：保留原有的运行方式

## 📁 目录结构

```
tests/e2e/
├── __init__.py              # 模块初始化
├── README.md                # 本文档
├── .gitignore               # Git忽略文件
├── run_tests.py             # 主测试入口脚本
├── run_e2e_test.py          # 便捷运行脚本（兼容性）
├── config/                  # 配置管理
│   ├── __init__.py
│   └── config.py            # 测试配置
├── core/                    # 核心组件
│   ├── __init__.py
│   ├── validators/          # 验证器组件
│   │   ├── __init__.py
│   │   ├── celery_validator.py    # Celery验证
│   │   ├── redis_validator.py     # Redis验证
│   │   └── nfs_validator.py       # NFS验证
│   └── senders/             # 任务发送器
│       ├── __init__.py
│       └── task_sender.py         # 通用任务发送框架
├── runners/                 # 测试运行器
│   ├── __init__.py
│   └── main_e2e_test.py     # 主测试运行器
├── utils/                   # 工具和示例
│   ├── __init__.py
│   └── examples.py          # 使用示例
├── reports/                 # 测试报告输出目录
│   └── (自动生成的JSON报告)
└── logs/                    # 测试日志输出目录
    └── (自动生成的日志文件)
```

## 🚀 快速开始

### 1. 环境准备

确保以下服务正在运行：
- RabbitMQ (消息队列)
- Redis (结果存储)
- Celery Workers (任务处理)
- NFS存储 (文件存储)

### 2. 配置检查

测试工具会自动加载项目根目录下的环境配置文件：
- `.env.dev` - 开发环境配置
- `.env.prod` - 生产环境配置

### 3. 运行测试

#### 方式一：使用新的主入口脚本（推荐）

```bash
cd /path/to/AiLabel_python_backend

# 运行完整测试（开发环境）
python tests/e2e/run_tests.py --env dev

# 运行完整测试（生产环境）
python tests/e2e/run_tests.py --env prod

# 仅运行系统验证
python tests/e2e/run_tests.py --validate-only --env dev

# 仅运行单任务测试
python tests/e2e/run_tests.py --single-task --env dev --image-type 3

# 仅运行批量测试
python tests/e2e/run_tests.py --batch-only --env dev --batch-count 5

# 跳过批量测试的完整测试
python tests/e2e/run_tests.py --env dev --no-batch
```

#### 方式二：交互式菜单

```bash
cd /path/to/AiLabel_python_backend
python tests/e2e/run_e2e_test.py
```

#### 方式三：直接使用测试运行器

```bash
# 使用主测试运行器
python tests/e2e/runners/main_e2e_test.py --env dev

# 命令行快捷方式
python tests/e2e/run_e2e_test.py validate dev
python tests/e2e/run_e2e_test.py single dev
python tests/e2e/run_e2e_test.py batch dev
python tests/e2e/run_e2e_test.py full dev
```

## 🔧 测试组件说明

### 1. 系统验证 (System Validation)

验证系统基础设施是否正常：

- **Celery验证**：检查worker进程、队列连接、任务处理能力
- **Redis验证**：检查连接状态、数据操作、任务存储
- **NFS验证**：检查存储可访问性、权限设置

### 2. 任务发送框架 (Task Sender)

通用的任务发送器，支持：

- **多种图像类型**：普通图像(1)、DICOM图像(2)、病理图像(3)、多通道图像(4)
- **批量发送**：支持发送多个任务进行压力测试
- **可扩展设计**：易于添加新的任务类型

### 3. 文件验证 (NFS Validator)

根据图像类型验证对应的输出文件：

- **Type 1/2**：主图片 + 缩略图
- **Type 3**：DeepZoom metadata.xml
- **Type 4**：多通道 DeepZoom metadata.xml

### 4. 生命周期监控

- **Redis任务监控**：跟踪任务状态变化
- **NFS文件监控**：监控文件生成过程
- **超时处理**：避免无限等待

## 📊 测试报告

测试完成后会生成：

1. **控制台输出**：实时显示测试进度和结果
2. **日志文件**：详细的测试日志 (`e2e_test_YYYYMMDD_HHMMSS.log`)
3. **JSON报告**：结构化的测试结果 (`e2e_report_YYYYMMDD_HHMMSS.json`)

### 报告内容

- 测试环境信息
- 各阶段执行结果
- 性能指标（耗时、成功率等）
- 问题诊断和建议

## 🎯 使用场景

### 1. 开发环境验证

```bash
# 快速检查开发环境是否正常
python tests/e2e/run_tests.py --validate-only --env dev
```

### 2. 部署前验证

```bash
# 完整测试确保系统可以部署
python tests/e2e/run_tests.py --env prod
```

### 3. 性能测试

```bash
# 批量任务测试系统负载能力
python tests/e2e/run_tests.py --batch-only --env prod --batch-count 10
```

### 4. 问题诊断

当系统出现问题时，运行端到端测试可以快速定位问题所在：

- Celery worker是否正常
- Redis连接是否稳定
- NFS存储是否可访问
- 任务处理流程是否完整

## ⚙️ 配置选项

### 测试配置 (config.py)

```python
test_config = {
    'test_timeout': 300,      # 测试超时时间（秒）
    'retry_attempts': 3,      # 重试次数
    'retry_delay': 5,         # 重试间隔（秒）
    'cleanup_after_test': True,  # 测试后是否清理
    'log_level': 'INFO'       # 日志级别
}
```

### 环境配置

测试工具会读取以下环境变量：

- `RABBITMQ_HOST`, `RABBITMQ_PORT` - RabbitMQ连接
- `REDIS_HOST`, `REDIS_PORT` - Redis连接
- `MYSQL_HOST`, `MYSQL_PORT` - 数据库连接
- `PROJECT_SAVE_DIR` - NFS存储路径

## 🔍 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认服务端口是否正确
   - 检查防火墙设置

2. **权限错误**
   - 检查NFS挂载权限
   - 确认用户有读写权限

3. **任务处理失败**
   - 检查Celery worker日志
   - 确认图像文件路径是否存在
   - 检查依赖库是否安装

### 调试模式

设置更详细的日志级别：

```python
# 在config.py中修改
test_config = {
    'log_level': 'DEBUG'  # 显示更多调试信息
}
```

## 🚀 扩展开发

### 添加新的任务类型

1. 在 `task_sender.py` 中继承 `TaskSenderBase`
2. 实现 `create_task_data()` 和 `get_queue_name()` 方法
3. 在 `nfs_validator.py` 中添加对应的文件验证逻辑

### 添加新的验证组件

1. 创建新的验证器类
2. 实现标准的验证接口
3. 在 `main_e2e_test.py` 中集成新组件

## 📝 最佳实践

1. **定期运行**：建议在每次部署前运行完整测试
2. **环境隔离**：开发和生产环境使用不同的配置
3. **日志保留**：保留测试日志用于问题追踪
4. **监控集成**：可以将测试结果集成到监控系统

## 📞 支持

如有问题或建议，请：

1. 查看测试日志文件
2. 检查系统配置
3. 联系开发团队
