# 端到端测试依赖包
# E2E Testing Dependencies for AiLabel

# 核心依赖 - Core Dependencies
pika>=1.3.0                    # RabbitMQ客户端
redis>=4.0.0                   # Redis客户端
python-dotenv>=0.19.0          # 环境变量加载
psutil>=5.8.0                  # 系统进程监控

# 数据库相关 - Database
pymysql>=1.0.0                 # MySQL连接器
SQLAlchemy>=1.4.0              # ORM框架

# 测试框架 - Testing Framework
pytest>=7.0.0                  # 测试框架
pytest-asyncio>=0.21.0         # 异步测试支持
pytest-timeout>=2.1.0          # 测试超时控制
pytest-xdist>=3.0.0            # 并行测试执行

# 日志和监控 - Logging & Monitoring
colorlog>=6.6.0                # 彩色日志输出
structlog>=22.0.0              # 结构化日志

# 工具库 - Utilities
click>=8.0.0                   # 命令行工具
tabulate>=0.9.0                # 表格格式化输出
tqdm>=4.64.0                   # 进度条显示

# HTTP客户端 - HTTP Client (如果需要API测试)
requests>=2.28.0               # HTTP请求库
httpx>=0.24.0                  # 异步HTTP客户端

# 数据处理 - Data Processing
pandas>=1.5.0                  # 数据分析（用于测试报告）
numpy>=1.21.0                  # 数值计算

# 文件处理 - File Processing
pathlib2>=2.3.0               # 路径处理增强
watchdog>=2.1.0                # 文件系统监控

# 时间处理 - Time Handling
python-dateutil>=2.8.0        # 日期时间处理

# JSON处理 - JSON Processing
ujson>=5.4.0                   # 快速JSON处理

# 配置管理 - Configuration Management
pydantic>=1.10.0               # 数据验证和设置管理

# 并发处理 - Concurrency
# concurrent-futures>=3.1.1     # 并发执行 (Python 3.2+内置)
# asyncio-mqtt>=0.11.0           # 异步MQTT客户端（如果需要）

# 开发工具 - Development Tools
ipython>=8.0.0                 # 交互式Python
rich>=12.0.0                   # 富文本终端输出

# 性能分析 - Performance Analysis
memory-profiler>=0.60.0        # 内存使用分析
line-profiler>=4.0.0           # 代码行级性能分析

# 网络工具 - Network Tools
ping3>=4.0.0                   # 网络连通性测试
netifaces>=0.11.0              # 网络接口信息

# 系统监控 - System Monitoring
py-cpuinfo>=9.0.0              # CPU信息获取
GPUtil>=1.4.0                  # GPU监控（如果需要）

# 图像处理测试 - Image Processing Testing
Pillow>=9.0.0                  # 图像处理库
opencv-python>=4.6.0           # 计算机视觉库

# 文档生成 - Documentation
sphinx>=5.0.0                  # 文档生成
sphinx-rtd-theme>=1.0.0        # 文档主题

# 代码质量 - Code Quality
flake8>=5.0.0                  # 代码风格检查
black>=22.0.0                  # 代码格式化
isort>=5.10.0                  # 导入排序

# 安全扫描 - Security Scanning
bandit>=1.7.0                  # 安全漏洞扫描
safety>=2.0.0                  # 依赖安全检查
