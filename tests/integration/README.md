# 集成测试目录

本目录包含系统集成测试脚本，用于测试各组件之间的交互和整体系统功能。

## 📁 目录结构

```
tests/integration/
├── test_celery_task.py     # Celery任务集成测试
├── test_image_task.py      # 图像处理任务测试
├── test_rabbitmq_sender.py # RabbitMQ消息发送测试
└── README.md               # 本文档
```

## 🧪 测试脚本说明

### Celery任务集成测试 (test_celery_task.py)

测试Celery系统的连接和任务执行功能。

#### 功能特性

- **连接测试**: 验证Celery broker连接
- **Worker状态**: 检查worker进程状态
- **任务发送**: 测试任务发送和接收
- **结果获取**: 验证任务结果返回
- **错误处理**: 测试异常情况处理

#### 使用方法

```bash
# 运行Celery集成测试
python tests/integration/test_celery_task.py

# 测试特定功能
python tests/integration/test_celery_task.py --test-connection
python tests/integration/test_celery_task.py --test-workers
python tests/integration/test_celery_task.py --test-tasks
```

### 图像处理任务测试 (test_image_task.py)

专门测试图像转换和处理功能。

#### 功能特性

- **图像任务发送**: 发送图像处理任务到队列
- **多种图像类型**: 支持不同类型的图像处理测试
- **任务监控**: 监控任务执行状态
- **结果验证**: 验证处理结果的正确性
- **性能测试**: 测试处理性能和响应时间

#### 使用方法

```bash
# 运行图像处理测试
python tests/integration/test_image_task.py

# 测试特定图像类型
python tests/integration/test_image_task.py --image-type 1  # 普通图像
python tests/integration/test_image_task.py --image-type 2  # DICOM图像
python tests/integration/test_image_task.py --image-type 3  # 病理图像
python tests/integration/test_image_task.py --image-type 4  # 多通道图像

# 批量测试
python tests/integration/test_image_task.py --batch-count 10
```

### RabbitMQ消息发送测试 (test_rabbitmq_sender.py)

测试RabbitMQ消息队列的连接和消息传递功能。

#### 功能特性

- **连接测试**: 验证RabbitMQ连接
- **队列操作**: 测试队列创建和管理
- **消息发送**: 测试消息发送功能
- **消息接收**: 验证消息接收和处理
- **错误恢复**: 测试连接断开和恢复

#### 使用方法

```bash
# 运行RabbitMQ测试
python tests/integration/test_rabbitmq_sender.py

# 测试特定队列
python tests/integration/test_rabbitmq_sender.py --queue ailabel_queue
python tests/integration/test_rabbitmq_sender.py --queue medlabel_image_convert_queue

# 压力测试
python tests/integration/test_rabbitmq_sender.py --stress-test --message-count 1000
```

## 🚀 运行测试

### 环境准备

1. **启动依赖服务**
   ```bash
   # 启动RabbitMQ
   sudo systemctl start rabbitmq-server
   
   # 启动Redis
   sudo systemctl start redis
   
   # 启动Celery Worker
   ./scripts/celery/start_celery_dev.sh
   ```

2. **安装测试依赖**
   ```bash
   pip install -r tests/test_requirements.txt
   ```

### 运行单个测试

```bash
# 运行特定测试脚本
python tests/integration/test_celery_task.py
python tests/integration/test_image_task.py
python tests/integration/test_rabbitmq_sender.py
```

### 运行所有集成测试

```bash
# 使用pytest运行所有集成测试
python -m pytest tests/integration/ -v

# 生成测试报告
python -m pytest tests/integration/ --html=reports/integration_test_report.html
```

## 📊 测试报告

### 输出格式

测试脚本会生成以下输出：

1. **控制台输出**: 实时显示测试进度和结果
2. **日志文件**: 详细的测试执行日志
3. **测试报告**: 结构化的测试结果报告

### 示例输出

```
🔍 测试Celery连接...
✅ Celery连接成功
✅ 发现 3 个活跃的worker
✅ 任务发送成功: task_id_12345
✅ 任务执行完成: SUCCESS
📊 测试结果: 4/4 通过
```

## 🔧 配置和自定义

### 环境配置

测试脚本会自动加载环境配置：

```bash
# 开发环境
export CELERY_ENV=dev

# 生产环境
export CELERY_ENV=prod
```

### 自定义测试参数

可以通过环境变量或命令行参数自定义测试：

```bash
# 设置测试超时时间
export TEST_TIMEOUT=300

# 设置测试重试次数
export TEST_RETRY_COUNT=3

# 设置测试项目ID
export TEST_PROJECT_ID=17
```

## 📝 添加新测试

### 测试脚本模板

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新集成测试脚本模板
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

def test_new_feature():
    """测试新功能"""
    print("🔍 测试新功能...")
    
    try:
        # 测试逻辑
        result = perform_test()
        
        if result:
            print("✅ 新功能测试成功")
            return True
        else:
            print("❌ 新功能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 新功能集成测试")
    print("=" * 50)
    
    success = test_new_feature()
    
    if success:
        print("✅ 所有测试通过")
        sys.exit(0)
    else:
        print("❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### 最佳实践

1. **独立性**: 每个测试应该独立运行
2. **清理**: 测试后清理创建的资源
3. **错误处理**: 完善的异常处理机制
4. **日志记录**: 详细的测试日志
5. **参数化**: 支持不同的测试参数

## 🆘 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否启动
   - 验证网络连接
   - 确认端口配置

2. **任务超时**
   - 增加超时时间
   - 检查worker状态
   - 查看任务队列

3. **权限错误**
   - 检查文件权限
   - 确认用户权限
   - 验证目录访问

### 调试方法

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 运行单个测试函数
python -c "from tests.integration.test_celery_task import test_celery_connection; test_celery_connection()"

# 查看详细错误信息
python tests/integration/test_celery_task.py 2>&1 | tee test_debug.log
```

## 📞 支持

如遇到问题，请：

1. 查看测试输出和错误信息
2. 检查相关服务的日志
3. 确认环境配置是否正确
4. 参考端到端测试文档
5. 联系开发团队获取支持
