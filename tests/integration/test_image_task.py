#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理任务测试脚本
专门测试图像转换功能
"""

import pika
import json
import time
import os
import sys
import hashlib
import random
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

# 加载环境配置
load_dotenv(os.path.join(project_root, '.env.dev'))

# RabbitMQ配置
RABBITMQ_HOST = os.getenv('RABBITMQ_HOST', '**************')
RABBITMQ_PORT = int(os.getenv('RABBITMQ_PORT', '25675'))
RABBITMQ_ACCOUNT = os.getenv('RABBITMQ_ACCOUNT', 'admin')
RABBITMQ_PASSWORD = os.getenv('RABBITMQ_PASSWORD', 'vipa@404')

# 图像转换队列
IMAGE_CONVERT_QUEUE = 'medlabel_image_convert_queue'

def generate_random_hash():
    """生成随机的hash值作为图像名称"""
    random_string = f"{time.time()}-{random.randint(1000, 9999)}"
    return hashlib.md5(random_string.encode()).hexdigest()[:16]

def send_image_processing_task():
    """发送图像处理任务"""
    print("🎯 开始发送图像处理任务...")
    print(f"📋 RabbitMQ: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
    print(f"📋 队列: {IMAGE_CONVERT_QUEUE}")
    print("=" * 50)
    
    try:
        # 创建连接
        credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=RABBITMQ_HOST,
                port=RABBITMQ_PORT,
                virtual_host='/',
                credentials=credentials,
                heartbeat=0
            )
        )
        
        channel = connection.channel()
        
        # 声明队列（与应用中的配置保持一致）
        channel.queue_declare(
            queue=IMAGE_CONVERT_QUEUE,
            durable=True,
            arguments={
                'x-dead-letter-exchange': 'dlx.direct',
                'x-dead-letter-routing-key': 'image_convert.dlq'
            }
        )
        
        # 创建测试任务数据 - 使用您指定的参数
        random_hash = generate_random_hash()
        task_data = {
            "taskId": f"svs-task-{int(time.time())}-{random.randint(100, 999)}",
            "imageTypeId": 3,  # 3=病理图 (SVS文件)
            "projectId": "17",  # 项目ID为17
            "imageName": random_hash,  # 随机hash值作为图像名称
            "imageId": f"svs-img-{int(time.time())}-{random.randint(1000, 9999)}",
            "imageUrl": "/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/*********.svs",
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "original_filename": "*********.svs",
                "file_type": "svs",
                "source": "浙二医院",
                "date": "2022-10-18",
                "test_mode": True
            }
        }
        
        # 发送消息
        message_body = json.dumps(task_data, ensure_ascii=False, indent=2)
        
        channel.basic_publish(
            exchange='',
            routing_key=IMAGE_CONVERT_QUEUE,
            body=message_body,
            properties=pika.BasicProperties(
                delivery_mode=2,  # 持久化消息
                content_type='application/json',
                content_encoding='utf-8',
                timestamp=int(time.time())
            )
        )
        
        print(f"✅ 成功发送病理图处理任务!")
        print(f"📋 任务ID: {task_data['taskId']}")
        print(f"📋 图像类型: {task_data['imageTypeId']} (病理图-SVS)")
        print(f"📋 项目ID: {task_data['projectId']}")
        print(f"📋 图像名称: {task_data['imageName']} (随机hash)")
        print(f"📋 图像ID: {task_data['imageId']}")
        print(f"📋 SVS文件路径: {task_data['imageUrl']}")
        print(f"📋 来源: 浙二医院 (2022-10-18)")
        
        # 关闭连接
        connection.close()
        
        print("\n🔍 请检查以下位置查看处理结果:")
        print("   1. Celery日志: tail -f log/dev/celery.log")
        print("   2. Flask应用终端输出")
        print("   3. 应用日志文件")

        print("\n💡 病理图(SVS)处理说明:")
        print("   - 图像类型ID=3 表示病理图处理")
        print("   - 将调用 patho_image_process 函数")
        print("   - 会生成深度缩放切片 (DeepZoom)")
        print("   - 处理时间较长，请耐心等待")

        print("\n✅ 如果看到以下信息说明任务被正确处理:")
        print("   - 🎯 开始处理图像转换任务")
        print("   - 开始处理病理图文件")
        print("   - Celery任务已提交")
        print("   - ✅ 图像转换任务完成，消息已确认")
        
        return True
        
    except Exception as e:
        print(f"❌ 发送任务失败: {e}")
        return False

def send_multiple_tasks(count=3):
    """发送多个病理图测试任务"""
    print(f"\n🚀 发送 {count} 个病理图测试任务...")
    
    for i in range(count):
        print(f"\n📤 发送第 {i+1} 个病理图任务...")
        
        try:
            # 创建连接
            credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(
                    host=RABBITMQ_HOST,
                    port=RABBITMQ_PORT,
                    virtual_host='/',
                    credentials=credentials,
                    heartbeat=0
                )
            )
            
            channel = connection.channel()
            
            # 声明队列
            channel.queue_declare(
                queue=IMAGE_CONVERT_QUEUE,
                durable=True,
                arguments={
                    'x-dead-letter-exchange': 'dlx.direct',
                    'x-dead-letter-routing-key': 'image_convert.dlq'
                }
            )
            
            # 创建批量病理图任务数据
            random_hash = generate_random_hash()
            task_data = {
                "taskId": f"svs-batch-{i+1}-{int(time.time())}-{random.randint(100, 999)}",
                "imageTypeId": 3,  # 病理图
                "projectId": "17",  # 项目ID为17
                "imageName": f"{random_hash}_batch_{i+1}",  # 随机hash + 批次标识
                "imageId": f"svs-batch-img-{i+1}-{int(time.time())}-{random.randint(1000, 9999)}",
                "imageUrl": "/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/*********.svs",
                "batch_id": i+1,
                "total_tasks": count,
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "batch_test": True,
                    "source": "浙二医院"
                }
            }
            
            # 发送消息
            channel.basic_publish(
                exchange='',
                routing_key=IMAGE_CONVERT_QUEUE,
                body=json.dumps(task_data),
                properties=pika.BasicProperties(
                    delivery_mode=2,
                    content_type='application/json',
                    content_encoding='utf-8'
                )
            )
            
            print(f"   ✅ 任务 {i+1} 发送成功: {task_data['taskId']}")
            
            connection.close()
            time.sleep(1)  # 间隔1秒
            
        except Exception as e:
            print(f"   ❌ 任务 {i+1} 发送失败: {e}")

if __name__ == "__main__":
    print("🎯 病理图(SVS)处理任务测试脚本")
    print("=" * 50)
    
    # 发送单个测试任务
    success = send_image_processing_task()
    
    if success:
        # 等待一下，然后发送批量任务
        print("\n⏳ 等待3秒后发送批量任务...")
        time.sleep(3)
        
        # 发送多个任务
        send_multiple_tasks(3)
        
        print("\n🎉 所有测试任务发送完成!")
        print("\n📋 监控命令:")
        print("   tail -f log/dev/celery.log")
    else:
        print("\n❌ 测试失败，请检查RabbitMQ连接配置")
