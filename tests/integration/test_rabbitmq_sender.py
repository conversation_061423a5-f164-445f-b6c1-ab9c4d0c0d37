#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RabbitMQ消息发送测试脚本
用于测试标注平台的Celery Worker是否正常工作
"""

import pika
import json
import time
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

# 加载环境配置
load_dotenv(os.path.join(project_root, '.env.dev'))

# RabbitMQ配置
RABBITMQ_HOST = os.getenv('RABBITMQ_HOST', '**************')
RABBITMQ_PORT = int(os.getenv('RABBITMQ_PORT', '25675'))
RABBITMQ_ACCOUNT = os.getenv('RABBITMQ_ACCOUNT', 'admin')
RABBITMQ_PASSWORD = os.getenv('RABBITMQ_PASSWORD', 'vipa@404')

# 队列配置
CELERY_QUEUE = 'ailabel_queue'
IMAGE_CONVERT_QUEUE = 'medlabel_image_convert_queue'

def create_connection():
    """创建RabbitMQ连接"""
    try:
        credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=RABBITMQ_HOST,
                port=RABBITMQ_PORT,
                virtual_host='/',
                credentials=credentials,
                heartbeat=0
            )
        )
        return connection
    except Exception as e:
        print(f"❌ 连接RabbitMQ失败: {e}")
        return None

def send_celery_task(channel, task_name="test_task", task_args=None, task_kwargs=None):
    """发送Celery任务消息"""
    if task_args is None:
        task_args = []
    if task_kwargs is None:
        task_kwargs = {}
    
    # Celery消息格式
    task_id = f"test-{int(time.time())}"
    message = {
        "id": task_id,
        "task": task_name,
        "args": task_args,
        "kwargs": task_kwargs,
        "retries": 0,
        "eta": None,
        "expires": None,
        "utc": True,
        "callbacks": None,
        "errbacks": None,
        "timelimit": [None, None],
        "taskset": None,
        "chord": None
    }
    
    try:
        # 声明队列
        channel.queue_declare(queue=CELERY_QUEUE, durable=True)
        
        # 发送消息
        channel.basic_publish(
            exchange='',
            routing_key=CELERY_QUEUE,
            body=json.dumps(message),
            properties=pika.BasicProperties(
                delivery_mode=2,  # 持久化消息
                content_type='application/json',
                content_encoding='utf-8'
            )
        )
        
        print(f"✅ 已发送Celery任务: {task_name} (ID: {task_id})")
        return task_id
    except Exception as e:
        print(f"❌ 发送Celery任务失败: {e}")
        return None

def send_image_convert_task(channel, image_path="test_image.jpg"):
    """发送图像转换任务消息"""
    # 模拟图像转换任务数据
    task_data = {
        "task_id": f"img-convert-{int(time.time())}",
        "image_path": image_path,
        "source_format": "jpg",
        "target_format": "png",
        "timestamp": datetime.now().isoformat(),
        "user_id": "test_user",
        "project_id": "test_project"
    }
    
    try:
        # 声明队列
        channel.queue_declare(
            queue=IMAGE_CONVERT_QUEUE,
            durable=True,
            arguments={
                'x-dead-letter-exchange': 'dlx.direct',
                'x-dead-letter-routing-key': 'image_convert.dlq'
            }
        )
        
        # 发送消息
        channel.basic_publish(
            exchange='',
            routing_key=IMAGE_CONVERT_QUEUE,
            body=json.dumps(task_data),
            properties=pika.BasicProperties(
                delivery_mode=2,  # 持久化消息
                content_type='application/json',
                content_encoding='utf-8'
            )
        )
        
        print(f"✅ 已发送图像转换任务: {task_data['task_id']}")
        return task_data['task_id']
    except Exception as e:
        print(f"❌ 发送图像转换任务失败: {e}")
        return None

def send_test_messages():
    """发送测试消息"""
    print("🚀 开始发送测试消息到RabbitMQ...")
    print(f"📋 连接信息: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
    print(f"📋 用户: {RABBITMQ_ACCOUNT}")
    print("=" * 60)
    
    # 创建连接
    connection = create_connection()
    if not connection:
        return
    
    try:
        channel = connection.channel()
        
        # 1. 发送Celery测试任务
        print("\n📤 发送Celery测试任务...")
        celery_task_id = send_celery_task(
            channel,
            task_name="app.test_task",
            task_args=["Hello", "World"],
            task_kwargs={"test": True, "timestamp": time.time()}
        )
        
        # 2. 发送图像处理任务
        print("\n📤 发送图像转换任务...")
        image_task_id = send_image_convert_task(
            channel,
            image_path="/test/path/sample_image.jpg"
        )
        
        # 3. 发送多个测试任务
        print("\n📤 发送批量测试任务...")
        for i in range(3):
            task_id = send_celery_task(
                channel,
                task_name="app.batch_test_task",
                task_args=[f"batch_task_{i}"],
                task_kwargs={"batch_id": i, "total": 3}
            )
            time.sleep(0.5)  # 间隔0.5秒
        
        print("\n✅ 所有测试消息发送完成!")
        print("\n📋 请检查以下日志文件查看处理结果:")
        print("   - Celery日志: tail -f log/dev/celery.log")
        print("   - Flask日志: 查看终端输出")
        
    except Exception as e:
        print(f"❌ 发送消息时出错: {e}")
    finally:
        connection.close()
        print("\n🔌 已关闭RabbitMQ连接")

def check_queue_status():
    """检查队列状态"""
    print("\n🔍 检查队列状态...")
    
    connection = create_connection()
    if not connection:
        return
    
    try:
        channel = connection.channel()
        
        # 检查Celery队列
        try:
            method = channel.queue_declare(queue=CELERY_QUEUE, passive=True)
            print(f"📊 {CELERY_QUEUE} 队列消息数: {method.method.message_count}")
        except Exception as e:
            print(f"⚠️ 无法获取 {CELERY_QUEUE} 队列状态: {e}")
        
        # 检查图像转换队列
        try:
            method = channel.queue_declare(queue=IMAGE_CONVERT_QUEUE, passive=True)
            print(f"📊 {IMAGE_CONVERT_QUEUE} 队列消息数: {method.method.message_count}")
        except Exception as e:
            print(f"⚠️ 无法获取 {IMAGE_CONVERT_QUEUE} 队列状态: {e}")
            
    except Exception as e:
        print(f"❌ 检查队列状态失败: {e}")
    finally:
        connection.close()

if __name__ == "__main__":
    print("🎯 RabbitMQ消息发送测试脚本")
    print("=" * 60)
    
    # 检查队列状态
    check_queue_status()
    
    # 发送测试消息
    send_test_messages()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("   1. 运行 'tail -f log/dev/celery.log' 查看实时日志")
    print("   2. 检查Flask应用终端输出")
    print("   3. 如果看到任务处理日志，说明系统工作正常")
