# MedLabel 标注平台 Python 服务端

[![Python](https://img.shields.io/badge/Python-3.11-blue.svg)](https://www.python.org/)
[![Flask](https://img.shields.io/badge/Flask-3.1.1-green.svg)](https://flask.palletsprojects.com/)
[![Celery](https://img.shields.io/badge/Celery-5.5.2-red.svg)](https://docs.celeryproject.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🏥 专业的医学图像标注平台后端服务，基于 Flask + Celery 架构，支持多种医学图像格式的高性能处理

## 📖 目录

- [项目概述](#-项目概述)
- [系统架构](#-系统架构)
- [技术栈](#-技术栈)
- [支持格式](#-支持格式)
- [环境要求](#-环境要求)
- [快速开始](#-快速开始)
- [配置说明](#-配置说明)
- [部署指南](#-部署指南)
- [监控运维](#-监控运维)
- [故障排查](#-故障排查)
- [开发指南](#-开发指南)
- [API文档](#-api文档)
- [贡献指南](#-贡献指南)

## 🎯 项目概述

MedLabel 标注平台是一个专业的医学图像标注系统的后端服务，专注于处理各种医学图像格式的转换、预处理和标注任务。该平台采用分布式架构，支持高并发处理，确保医学图像处理的准确性和效率。

### 核心功能

- 🔄 **图像格式转换**: 支持多种医学图像格式的相互转换
- 🎨 **图像预处理**: 提供专业的医学图像预处理算法
- 📊 **分布式处理**: 基于 Celery 的分布式任务队列
- 🔍 **实时监控**: 完善的任务进度跟踪和状态监控
- 🛡️ **高可用性**: 支持多实例部署和故障恢复
- 📈 **性能优化**: 针对大型医学图像的性能优化

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "客户端层"
        Web[Web前端]
        API[API客户端]
    end

    subgraph "应用层"
        Flask[Flask Web服务]
        Celery[Celery Worker集群]
    end

    subgraph "消息队列层"
        RabbitMQ[RabbitMQ集群]
        Redis[Redis缓存]
    end

    subgraph "数据层"
        MySQL[MySQL数据库]
        NFS[NFS存储]
    end

    Web --> Flask
    API --> Flask
    Flask --> RabbitMQ
    RabbitMQ --> Celery
    Celery --> Redis
    Celery --> MySQL
    Celery --> NFS

    style Flask fill:#e1f5fe
    style Celery fill:#f3e5f5
    style RabbitMQ fill:#fff3e0
    style Redis fill:#ffebee
```

## 🛠️ 技术栈

### 核心框架
- **Python 3.11**: 主要开发语言
- **Flask 3.1.1**: 轻量级 Web 框架
- **Celery 5.5.2**: 分布式任务队列系统
- **SQLAlchemy 2.0.41**: ORM 数据库操作

### 消息队列与缓存
- **RabbitMQ**: 消息中间件，作为 Celery 的 broker
- **Redis 6.1.0**: 任务结果存储和缓存
- **Pika 1.3.2**: RabbitMQ Python 客户端

### 图像处理
- **OpenCV 4.11.0**: 计算机视觉库
- **Pillow 11.2.1**: Python 图像处理库
- **OpenSlide 1.4.2**: 病理图像处理
- **SimpleITK 2.5.0**: 医学图像处理
- **TiffFile 2025.5.10**: TIFF 格式支持

### 数据库与存储
- **PyMySQL 1.1.1**: MySQL 数据库连接
- **Flask-SQLAlchemy 3.1.1**: Flask 数据库扩展

## 📋 支持格式

### 医学图像格式
| 格式类型 | 支持格式 | 描述 |
|---------|---------|------|
| 🖼️ **自然图像** | `png`, `jpg`, `jpeg` | 常见图像格式 |
| 🏥 **数字医学图像** | `dicom`, `dcm` | DICOM 医学影像标准 |
| 🔬 **病理图像** | `svs`, `tif`, `tiff`, `kfb` | 数字病理切片 |
| 🌈 **多重荧光图像** | `qptiff` | 量化病理图像 |

### 处理能力
- ✅ 大型图像文件处理（支持 GB 级别）
- ✅ 多层级图像金字塔生成
- ✅ 实时进度跟踪
- ✅ 批量处理支持

## 💻 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Python**: 3.11+
- **内存**: 8GB+ (推荐 16GB+)
- **存储**: 100GB+ 可用空间
- **网络**: 稳定的网络连接

### 依赖服务
- **RabbitMQ**: 3.8+
- **Redis**: 6.0+
- **MySQL**: 8.0+
- **Conda**: Miniconda 或 Anaconda

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建 Conda 环境
conda create -n myenv python=3.11
conda activate myenv

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env.prod  # 生产环境
cp .env.example .env.dev   # 开发环境

# 编辑配置文件
vim .env.prod
```

### 3. 启动服务

#### 方式一：使用管理脚本（推荐）

```bash
# 生产环境启动
./start_celery_prod.sh

# 开发环境启动
./start_celery_dev.sh

# 查看实时日志
tail -f ./log/prod/celery.log
```

#### 方式二：手动启动

```bash
# 激活环境
conda activate myenv

# 设置环境变量
export CELERY_ENV=prod

# 启动 Celery Worker
celery -A app.celery worker \
    --loglevel=INFO \
    --queues=ailabel_queue \
    --concurrency=10 \
    --pool=prefork
```

### 4. 验证服务

```bash
# 检查进程状态
ps aux | grep celery

# 检查队列状态
python test_queue.py

# 查看日志
tail -f ./log/celery.log
```

## ⚙️ 配置说明

### 环境配置文件

项目支持多环境配置，通过环境变量 `CELERY_ENV` 控制：

```bash
# 开发环境
export CELERY_ENV=dev

# 生产环境
export CELERY_ENV=prod
```

### 主要配置项

| 配置项 | 环境变量 | 默认值 | 说明 |
|-------|---------|--------|------|
| 数据库主机 | `MYSQL_HOST` | `**************` | MySQL 服务器地址 |
| 数据库端口 | `MYSQL_PORT` | `23306` | MySQL 端口 |
| 数据库名称 | `MYSQL_DB` | `medlabel` | 数据库名 |
| RabbitMQ主机 | `RABBITMQ_HOST` | `**************` | RabbitMQ 服务器 |
| RabbitMQ端口 | `RABBITMQ_PORT` | `25675` | RabbitMQ 端口 |
| Redis主机 | `REDIS_HOST` | `**************` | Redis 服务器 |
| Redis端口 | `REDIS_PORT` | `26379` | Redis 端口 |
| 项目目录 | `PROJECT_SAVE_DIR` | `/nfs5/medlabel/medlabel_212/projects` | 图像存储路径 |

### 队列配置

标注平台使用专用队列，与诊断平台隔离：

- **队列名称**: `ailabel_queue`
- **交换机**: `ailabel_exchange`
- **路由键**: `ailabel_routing_key`

## 🚀 部署指南

### 生产环境部署

#### 1. 系统准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装系统依赖
sudo apt install -y build-essential python3-dev libssl-dev libffi-dev

# 安装 Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh
```

#### 2. 服务配置

```bash
# 克隆项目
git clone <repository-url>
cd aiLabel_python_backend

# 创建环境配置
cp .env.example .env.prod
vim .env.prod

# 设置权限
chmod +x *.sh
```

#### 3. 启动服务

```bash
# 生产环境启动
./start_celery_prod.sh

# 验证启动
./status_check.sh
```

### 开发环境部署

```bash
# 开发环境启动
./start_celery_dev.sh

# 开发模式调试
export DEBUG_FLAG=true
python -m app.celery worker --loglevel=debug
```

## 📊 监控运维

### 服务管理脚本

#### 启动脚本特性

**`start_celery.sh` / `start_celery_prod.sh` / `start_celery_dev.sh`**

- ✅ 自动激活 conda 环境 `myenv`
- ✅ 智能清理已存在的 Celery 进程（仅当前用户）
- ✅ 混合日志轮转机制：
  - 每次启动备份当前日志
  - 单个日志文件最大 50MB
  - 自动保留最新 10 份备份
- ✅ 启动验证和状态检查
- ✅ 彩色日志输出，便于监控

**`stop_celery.sh` / `stop_celery_prod.sh` / `stop_celery_dev.sh`**

- ✅ 优雅停止 Celery 进程
- ✅ 智能进程清理（仅当前用户）
- ✅ 详细的停止状态反馈

### 日志管理

#### 日志结构
```
log/
├── celery.log                    # 当前日志
├── celery_20241201_143000.log    # 时间备份
├── celery_20241201_150000_size.log # 大小备份
├── dev/                          # 开发环境日志
│   └── celery.log
└── prod/                         # 生产环境日志
    └── celery.log
```

#### 日志特性
- 📁 **日志目录**: `./log/`
- 📄 **当前日志**: `./log/celery.log`
- 📦 **备份格式**: `./log/celery_YYYYMMDD_HHMMSS.log`
- 🔄 **自动轮转**: 超过 50MB 或每次启动
- 🗂️ **备份保留**: 最多 10 份

#### 日志查看命令

```bash
# 查看实时日志
tail -f ./log/celery.log

# 查看所有日志备份
ls -lt ./log/celery_*.log

# 搜索错误日志
grep -i error ./log/celery.log

# 查看特定时间段日志
grep "2024-12-01 14:" ./log/celery.log
```

### 性能监控

#### 系统监控指标

```bash
# CPU 和内存使用情况
top -p $(pgrep -f "celery.*worker")

# 磁盘使用情况
df -h /nfs5/medlabel/

# 网络连接状态
netstat -an | grep :25675  # RabbitMQ
netstat -an | grep :26379  # Redis
```

#### 队列监控

```bash
# 检查队列状态
python test_queue.py

# RabbitMQ 管理界面
# http://**************:15675

# Redis 连接测试
redis-cli -h ************** -p 26379 ping
```

## 🔧 故障排查

### 常见问题及解决方案

#### 1. Celery 连接超时

**问题现象**:
```
ConnectionResetError: [Errno 104] Connection reset by peer
```

**解决方案**:
```bash
# 检查 RabbitMQ 服务状态
sudo systemctl status rabbitmq-server

# 重启 RabbitMQ 服务
sudo systemctl restart rabbitmq-server

# 检查网络连接
telnet ************** 25675
```

#### 2. 内存不足

**问题现象**:
```
MemoryError: Unable to allocate array
```

**解决方案**:
```bash
# 检查内存使用
free -h

# 调整 Celery 并发数
celery -A app.celery worker --concurrency=5

# 清理临时文件
rm -rf /tmp/celery_*
```

#### 3. 图像处理失败

**问题现象**:
```
OpenSlideError: Cannot open file
```

**解决方案**:
```bash
# 检查文件权限
ls -la /nfs5/medlabel/medlabel_212/projects/

# 检查磁盘空间
df -h /nfs5/

# 验证图像文件
file /path/to/image.svs
```

#### 4. 队列堵塞

**问题现象**:
- 任务长时间处于 PENDING 状态
- 队列长度持续增长

**解决方案**:
```bash
# 清空队列
celery -A app.celery purge

# 重启 Worker
./stop_celery.sh && ./start_celery.sh

# 增加 Worker 数量
celery -A app.celery worker --concurrency=20
```

### 调试模式

#### 启用详细日志

```bash
# 设置调试模式
export DEBUG_FLAG=true
export CELERY_ENV=dev

# 启动调试模式
celery -A app.celery worker --loglevel=debug
```

#### 性能分析

```bash
# 启用性能分析
celery -A app.celery worker --loglevel=info --time-limit=3600

# 监控任务执行时间
grep "Task.*succeeded" ./log/celery.log | tail -20
```

## 👨‍💻 开发指南

### 项目结构

```
aiLabel_python_backend/
├── app/                          # 应用核心代码
│   ├── __init__.py              # Flask 应用初始化
│   ├── image_process.py         # 图像处理模块
│   ├── info.py                  # 信息接口
│   └── templates/               # 模板文件
├── asm/                         # 算法模块
│   ├── datas/                   # 数据处理
│   └── utils/                   # 工具函数
├── docs/                        # 文档目录
│   ├── architecture-analysis.md # 架构分析
│   ├── testing-guide.md        # 测试指南
│   └── high-load-configuration.md # 高负载配置
├── log/                         # 日志目录
├── ssh_scripts/                 # SSH 连接脚本
├── config.py                    # 配置文件
├── requirements.txt             # 依赖列表
├── start_celery*.sh            # 启动脚本
├── stop_celery*.sh             # 停止脚本
└── test_queue.py               # 队列测试
```

### 开发环境设置

```bash
# 1. 创建开发分支
git checkout -b feature/your-feature

# 2. 设置开发环境
export CELERY_ENV=dev
export DEBUG_FLAG=true

# 3. 启动开发服务
./start_celery_dev.sh

# 4. 运行测试
python -m pytest tests/

# 5. 代码格式化
black app/ asm/
flake8 app/ asm/
```

### 添加新功能

#### 1. 创建新的任务

```python
# app/new_task.py
from app import celery

@celery.task(bind=True)
def new_image_task(self, image_path, options):
    """新的图像处理任务"""
    try:
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100})

        # 执行处理逻辑
        result = process_image(image_path, options)

        return {'status': 'SUCCESS', 'result': result}
    except Exception as exc:
        self.update_state(state='FAILURE', meta={'error': str(exc)})
        raise
```

#### 2. 注册新队列

```python
# config.py
TASK_ROUTES = {
    'app.new_task.new_image_task': {'queue': 'new_task_queue'}
}
```

### 测试指南

#### 单元测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_image_process.py

# 生成覆盖率报告
python -m pytest --cov=app tests/
```

#### 集成测试

```bash
# 测试队列连接
python test_queue.py

# 测试图像处理
python -c "from app.image_process import test_image_conversion; test_image_conversion()"
```

## 📚 API文档

### 队列接口

#### 图像转换队列

**队列名称**: `medlabel_image_convert_queue`

**消息格式**:
```json
{
    "image_id": "12345",
    "image_path": "/path/to/image.svs",
    "output_format": "dzi",
    "options": {
        "tile_size": 256,
        "overlap": 1,
        "quality": 90
    }
}
```

**响应格式**:
```json
{
    "status": "SUCCESS",
    "task_id": "celery-task-id",
    "result": {
        "output_path": "/path/to/output/",
        "processing_time": 120.5,
        "tile_count": 1024
    }
}
```

### 任务状态

#### 状态类型

| 状态 | 描述 | 说明 |
|------|------|------|
| `PENDING` | 等待中 | 任务已提交，等待处理 |
| `PROGRESS` | 处理中 | 任务正在执行 |
| `SUCCESS` | 成功 | 任务完成 |
| `FAILURE` | 失败 | 任务执行失败 |
| `RETRY` | 重试中 | 任务重试执行 |

#### 进度查询

```python
from app import celery

# 查询任务状态
result = celery.AsyncResult(task_id)
print(f"状态: {result.state}")
print(f"进度: {result.info}")
```

### 错误代码

| 错误代码 | 描述 | 解决方案 |
|---------|------|---------|
| `IMG_001` | 图像文件不存在 | 检查文件路径 |
| `IMG_002` | 图像格式不支持 | 使用支持的格式 |
| `IMG_003` | 内存不足 | 增加系统内存或减少并发 |
| `IMG_004` | 磁盘空间不足 | 清理磁盘空间 |
| `QUEUE_001` | 队列连接失败 | 检查 RabbitMQ 服务 |
| `QUEUE_002` | 任务超时 | 增加超时时间 |

## 🤝 贡献指南

### 贡献流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/aiLabel_python_backend.git
   cd aiLabel_python_backend
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   # 设置开发环境
   export CELERY_ENV=dev

   # 运行测试
   python -m pytest tests/

   # 代码格式化
   black app/ asm/
   flake8 app/ asm/
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add new image processing feature"
   ```

5. **推送和创建 PR**
   ```bash
   git push origin feature/your-feature-name
   # 在 GitHub 上创建 Pull Request
   ```

### 代码规范

#### Python 代码风格

- 使用 [Black](https://black.readthedocs.io/) 进行代码格式化
- 使用 [Flake8](https://flake8.pycqa.org/) 进行代码检查
- 遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 规范

#### 提交信息规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(image): add support for QPTIFF format

- Add QPTIFF reader implementation
- Update image processing pipeline
- Add unit tests for QPTIFF support

Closes #123
```

### 开发最佳实践

#### 1. 错误处理

```python
import logging
from app import logger

def process_image(image_path):
    try:
        # 处理逻辑
        result = do_processing(image_path)
        logger.info(f"图像处理成功: {image_path}")
        return result
    except Exception as e:
        logger.error(f"图像处理失败: {image_path}, 错误: {str(e)}")
        raise
```

#### 2. 任务进度更新

```python
@celery.task(bind=True)
def long_running_task(self, data):
    total = len(data)
    for i, item in enumerate(data):
        # 更新进度
        self.update_state(
            state='PROGRESS',
            meta={'current': i, 'total': total, 'status': f'处理 {item}'}
        )
        process_item(item)

    return {'status': 'SUCCESS', 'processed': total}
```

#### 3. 资源管理

```python
import contextlib

@contextlib.contextmanager
def managed_resource(resource_path):
    resource = acquire_resource(resource_path)
    try:
        yield resource
    finally:
        release_resource(resource)

# 使用示例
with managed_resource('/path/to/image') as image:
    process_image(image)
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: MedLabel Team
- **邮箱**: <EMAIL>
- **文档**: [项目文档](docs/)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/aiLabel_python_backend/issues)

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

**最后更新**: 2024-12-01
**版本**: v2.0.0
**文档版本**: v1.0